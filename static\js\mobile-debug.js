/**
 * أداة تشخيص واجهة الهاتف المحمول
 * تساعد في اكتشاف وحل مشاكل العرض
 */

// إضافة أداة تشخيص للواجهة
function debugMobileInterface() {
    console.log('🔍 بدء تشخيص واجهة الهاتف المحمول...');
    
    // فحص العناصر الأساسية
    const mobileView = document.querySelector('.mobile-view');
    const containerFluid = document.querySelector('.container-fluid');
    const bootstrapMobile = document.querySelector('.d-md-none');
    
    console.log('📱 عناصر الواجهة:');
    console.log('- .mobile-view:', mobileView ? 'موجود' : 'غير موجود');
    console.log('- .container-fluid:', containerFluid ? 'موجود' : 'غير موجود');
    console.log('- .d-md-none:', bootstrapMobile ? 'موجود' : 'غير موجود');
    
    // فحص حالة العرض
    if (mobileView) {
        const computedStyle = window.getComputedStyle(mobileView);
        console.log('📱 حالة عرض .mobile-view:');
        console.log('- display:', computedStyle.display);
        console.log('- visibility:', computedStyle.visibility);
        console.log('- opacity:', computedStyle.opacity);
        console.log('- z-index:', computedStyle.zIndex);
    }
    
    if (containerFluid) {
        const computedStyle = window.getComputedStyle(containerFluid);
        console.log('🖥️ حالة عرض .container-fluid:');
        console.log('- display:', computedStyle.display);
        console.log('- visibility:', computedStyle.visibility);
    }
    
    // فحص فئات الجسم
    console.log('🏷️ فئات الجسم:');
    console.log('- mobile-device:', document.body.classList.contains('mobile-device'));
    console.log('- tablet-device:', document.body.classList.contains('tablet-device'));
    console.log('- desktop-device:', document.body.classList.contains('desktop-device'));
    console.log('- iphone-device:', document.body.classList.contains('iphone-device'));
    
    // فحص معلومات الجهاز
    if (window.deviceDetector) {
        console.log('📱 معلومات الجهاز:');
        console.log('- النوع:', window.deviceDetector.deviceType);
        console.log('- الطراز:', window.deviceDetector.deviceModel);
        console.log('- الشاشة:', `${window.deviceDetector.screenWidth}x${window.deviceDetector.screenHeight}`);
        console.log('- نسبة البكسل:', window.deviceDetector.pixelRatio);
        console.log('- الاتجاه:', window.deviceDetector.orientation);
        console.log('- هاتف محمول:', window.deviceDetector.isMobile);
        console.log('- جهاز لوحي:', window.deviceDetector.isTablet);
        console.log('- كمبيوتر:', window.deviceDetector.isDesktop);
    } else {
        console.log('❌ كاشف الأجهزة غير متاح');
    }
    
    // فحص أبعاد النافذة
    console.log('📏 أبعاد النافذة:');
    console.log('- العرض:', window.innerWidth);
    console.log('- الارتفاع:', window.innerHeight);
    console.log('- نسبة البكسل:', window.devicePixelRatio);
    
    // فحص User Agent
    console.log('🔍 User Agent:', navigator.userAgent);
    
    console.log('✅ انتهى التشخيص');
}

// إضافة زر تشخيص للواجهة (للتطوير فقط)
function addDebugButton() {
    // إنشاء زر التشخيص
    const debugBtn = document.createElement('button');
    debugBtn.innerHTML = '🔍 تشخيص';
    debugBtn.style.cssText = `
        position: fixed;
        top: 10px;
        left: 10px;
        z-index: 9999;
        background: #007bff;
        color: white;
        border: none;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    `;
    
    debugBtn.addEventListener('click', debugMobileInterface);
    document.body.appendChild(debugBtn);
    
    // إضافة زر إعادة تحميل كاشف الأجهزة
    const reloadBtn = document.createElement('button');
    reloadBtn.innerHTML = '🔄 إعادة كشف';
    reloadBtn.style.cssText = `
        position: fixed;
        top: 50px;
        left: 10px;
        z-index: 9999;
        background: #28a745;
        color: white;
        border: none;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    `;
    
    reloadBtn.addEventListener('click', () => {
        if (window.deviceDetector) {
            window.deviceDetector.checkDeviceType();
            console.log('🔄 تم إعادة تشغيل كاشف الأجهزة');
        } else {
            console.log('❌ كاشف الأجهزة غير متاح');
        }
    });
    document.body.appendChild(reloadBtn);
    
    // إضافة زر إظهار/إخفاء واجهة الهاتف يدوياً
    const toggleBtn = document.createElement('button');
    toggleBtn.innerHTML = '📱 تبديل واجهة';
    toggleBtn.style.cssText = `
        position: fixed;
        top: 90px;
        left: 10px;
        z-index: 9999;
        background: #ffc107;
        color: black;
        border: none;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    `;
    
    toggleBtn.addEventListener('click', () => {
        const mobileView = document.querySelector('.mobile-view');
        const containerFluid = document.querySelector('.container-fluid');
        
        if (mobileView && containerFluid) {
            if (mobileView.style.display === 'none' || !mobileView.style.display) {
                // إظهار واجهة الهاتف
                mobileView.style.display = 'block';
                mobileView.style.visibility = 'visible';
                containerFluid.style.display = 'none';
                console.log('📱 تم إظهار واجهة الهاتف يدوياً');
            } else {
                // إظهار واجهة الكمبيوتر
                mobileView.style.display = 'none';
                containerFluid.style.display = 'block';
                console.log('🖥️ تم إظهار واجهة الكمبيوتر يدوياً');
            }
        }
    });
    document.body.appendChild(toggleBtn);
}

// تشغيل أدوات التشخيص عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    // إضافة أزرار التشخيص (للتطوير فقط)
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        addDebugButton();
    }
    
    // تشغيل التشخيص التلقائي
    setTimeout(() => {
        debugMobileInterface();
    }, 2000);
});

// إضافة دالة عامة للتشخيص
window.debugMobile = debugMobileInterface;
