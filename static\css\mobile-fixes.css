/**
 * إصلاحات واجهة الهاتف المحمول - Loacker
 * تم إنشاؤه لحل مشاكل التضارب بين الواجهات
 */

/* إخفاء واجهة Bootstrap المحمولة افتراضياً */
.d-md-none {
    display: none !important;
}

/* إظهار واجهة Bootstrap فقط على الأجهزة اللوحية والكمبيوتر */
@media (min-width: 768px) {
    .d-md-none {
        display: block !important;
    }
}

/* التأكد من إخفاء واجهة الهاتف المخصصة على الأجهزة الكبيرة */
@media (min-width: 768px) {
    .mobile-view {
        display: none !important;
    }
}

/* إصلاحات للأجهزة المحمولة */
@media (max-width: 767px) {
    /* إخفاء واجهة سطح المكتب */
    .container-fluid {
        display: none !important;
    }
    
    /* إخفاء واجهة Bootstrap المحمولة */
    .d-md-none {
        display: none !important;
    }
    
    /* إظهار واجهة الهاتف المخصصة فقط للأجهزة المحمولة */
    body.mobile-device .mobile-view {
        display: block !important;
    }
}

/* إصلاحات خاصة بـ iPhone */
.iphone-device .mobile-view {
    /* تحسينات خاصة بـ iPhone */
    -webkit-overflow-scrolling: touch;
    -webkit-user-select: none;
    -webkit-touch-callout: none;
}

/* إصلاحات للشاشات الصغيرة جداً */
@media (max-width: 375px) {
    .mobile-view {
        font-size: 14px;
    }
    
    .mobile-header h1 {
        font-size: 1.5rem !important;
    }
    
    .mobile-store-card .card-title {
        font-size: 1rem !important;
    }
}

/* إصلاحات للشاشات الكبيرة (iPhone Plus, iPhone Pro Max) */
@media (min-width: 414px) and (max-width: 767px) {
    .mobile-view {
        font-size: 16px;
    }
    
    .mobile-header h1 {
        font-size: 2rem !important;
    }
}

/* إصلاحات للتوجه الأفقي على الهواتف */
@media (max-width: 767px) and (orientation: landscape) {
    .mobile-header {
        height: 50px;
    }
    
    .mobile-content {
        margin-top: 50px;
    }
    
    .mobile-map {
        height: 35vh;
    }
}

/* إصلاحات للتوجه العمودي */
@media (max-width: 767px) and (orientation: portrait) {
    .mobile-map {
        height: 45vh;
    }
}

/* إصلاحات عامة للتأكد من عمل الواجهة */
.mobile-view * {
    box-sizing: border-box;
}

/* إصلاح مشكلة التمرير */
.mobile-content {
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

/* إصلاح مشكلة الخريطة */
.mobile-map .leaflet-container {
    background: #f8f9fa;
}

/* إصلاح أزرار التبويبات */
.mobile-tabs {
    border-top: 1px solid #e9ecef;
}

.mobile-tab-btn {
    min-height: 60px;
    touch-action: manipulation;
}

/* إصلاح النماذج */
.mobile-form .form-control,
.mobile-form .form-select {
    min-height: 44px; /* الحد الأدنى لسهولة اللمس */
    font-size: 16px; /* منع التكبير التلقائي في iOS */
}

/* إصلاح الأزرار */
.mobile-form .btn {
    min-height: 44px;
    touch-action: manipulation;
}

/* إصلاح قائمة المتاجر */
.mobile-store-list {
    padding-bottom: 20px;
}

.mobile-store-card {
    touch-action: manipulation;
    cursor: pointer;
}

/* إصلاحات للمساحات الآمنة في iOS */
@supports (padding: max(0px)) {
    .mobile-header {
        padding-left: max(20px, env(safe-area-inset-left));
        padding-right: max(20px, env(safe-area-inset-right));
        padding-top: env(safe-area-inset-top);
    }
    
    .mobile-content {
        padding-left: max(15px, env(safe-area-inset-left));
        padding-right: max(15px, env(safe-area-inset-right));
    }
    
    .mobile-tabs {
        padding-left: env(safe-area-inset-left);
        padding-right: env(safe-area-inset-right);
        padding-bottom: env(safe-area-inset-bottom);
    }
}

/* إصلاح مشكلة الفلاش عند التحميل */
.mobile-view {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.mobile-view.loaded {
    opacity: 1;
}

/* إصلاح مشكلة التمرير الأفقي */
body.mobile-device {
    overflow-x: hidden;
}

/* إصلاح مشكلة ارتفاع الشاشة على الهواتف */
@media (max-width: 767px) {
    html, body {
        height: 100%;
        overflow-x: hidden;
    }
    
    body.mobile-device {
        position: fixed;
        width: 100%;
        height: 100%;
    }
}
