<!DOCTYPE html>
<html lang="ar" data-bs-theme="dark" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#212529">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    {% if current_user.is_authenticated %}
    <meta name="user-role" content="{{ current_user.role_id }}">
    {% endif %}
    <title>Loacker</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.replit.com/agent/bootstrap-agent-dark-theme.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />

    <!-- Google Fonts - Tajawal & Playfair Display (Classic European Font) -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&family=Playfair+Display:wght@400;700;900&display=swap">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <!-- Mobile CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/mobile.css') }}">
    <!-- Mobile Buttons CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/mobile-buttons.css') }}">
    <!-- Device Specific CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/device-specific.css') }}">
    <!-- Mobile Fixes CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/mobile-fixes.css') }}">
    <!-- Notification Badge CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/notification-badge.css') }}">
</head>
<body>
    <!-- واجهة الكمبيوتر -->
    <div class="container-fluid py-3">
        <header class="mb-4">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div></div> <!-- عنصر فارغ للمحاذاة -->
                <div class="text-end">
                    {% if current_user.is_authenticated %}
                        <a href="{{ url_for('profile') }}" class="btn btn-outline-info me-2">
                            <i class="fas fa-user"></i> الملف الشخصي
                        </a>
                        {% if current_user.role_id == 1 %}
                            <a href="{{ url_for('admin_panel') }}" class="btn btn-outline-primary me-2">
                                <i class="fas fa-cog"></i> لوحة التحكم
                            </a>
                            <a href="{{ url_for('statistics') }}" class="btn btn-outline-info me-2">
                                <i class="fas fa-chart-pie"></i> الإحصائيات
                            </a>
                            <a href="{{ url_for('admin_panel') }}#pendingStores" class="btn btn-outline-danger me-2 position-relative" id="notificationBell">
                                <i class="fas fa-bell"></i>
                                <span id="pendingStoresBadgeBell" class="notification-badge d-none">0</span>
                            </a>
                        {% endif %}
                        <a href="{{ url_for('logout') }}" class="btn btn-outline-danger">
                            <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                        </a>
                    {% else %}
                        <a href="{{ url_for('login') }}" class="btn btn-outline-primary">
                            <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                        </a>
                    {% endif %}
                </div>
            </div>

            <div class="text-center mb-4">
                <div class="logo-title-container">
                    <div class="logo-title-wrapper">
                        <div class="logo-circle-small">
                            <i class="fas fa-store"></i>
                        </div>
                        <h1 class="mb-2">
                            <span class="text-gradient">Loacker</span>
                        </h1>
                    </div>
                </div>
                <p class="lead text-muted">رسم خرائط وإدارة مواقع المتاجر بسهولة</p>
            </div>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            {% if not current_user.is_authenticated %}
            <div class="alert alert-info">
                <h4 class="alert-heading"><i class="fas fa-info-circle"></i> مرحباً بك في نظام Loacker!</h4>
                <p>يرجى <a href="{{ url_for('login') }}" class="alert-link">تسجيل الدخول</a> لعرض قائمة المتاجر والوصول إلى جميع ميزات النظام.</p>
                <hr>
                <p class="mb-0">بيانات تسجيل الدخول الافتراضية: <strong>admin / admin123</strong></p>
            </div>
            {% endif %}

            {% if current_user.is_authenticated %}
            <div class="row g-2 mb-3">
                <div class="col-12 col-sm-6">
                    <!-- تم حذف مربع البحث من هنا ونقله إلى قائمة المتاجر -->
                </div>
                <div class="col-12 col-sm-6">
                    <div class="d-flex gap-2 justify-content-between">
                        <button id="toggleMap" class="btn btn-outline-secondary flex-grow-1">
                            <i class="fas fa-list me-1"></i> <span class="d-none d-md-inline">عرض القائمة</span>
                        </button>
                        <button id="getCurrentLocation" class="btn btn-outline-info">
                            <i class="fas fa-location-arrow"></i>
                        </button>
                        <div class="d-flex gap-2">
                            <button id="shareSelectedStores" class="btn btn-outline-success" disabled>
                                <i class="fab fa-whatsapp"></i> <span id="selectedStoresCount">0</span>
                            </button>
                            <button id="deleteSelectedStores" class="btn btn-outline-danger" disabled>
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Alert Container -->
        <div id="alertContainer"></div>

        <!-- Tabs for form, map and list on mobile - will be shown on small screens -->
        <div class="d-md-none mb-3">
            <ul class="nav nav-pills nav-fill" id="mobileTabsNav" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="mobile-map-tab" data-bs-toggle="pill" data-bs-target="#mobile-map-content" type="button" role="tab">
                        <i class="fas fa-map"></i> الخريطة
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="mobile-list-tab" data-bs-toggle="pill" data-bs-target="#mobile-list-content" type="button" role="tab">
                        <i class="fas fa-list"></i> القائمة
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="mobile-form-tab" data-bs-toggle="pill" data-bs-target="#mobile-form-content" type="button" role="tab">
                        <i class="fas fa-plus-circle"></i> إضافة
                    </button>
                </li>
            </ul>
        </div>

        <div class="tab-content d-md-none" id="mobileTabsContent">
            <!-- Mobile: Map Tab -->
            <div class="tab-pane fade show active" id="mobile-map-content" role="tabpanel">
                <div class="card mb-3">
                    <div class="card-body p-0">
                        <div id="mobile-map" style="height: 70vh;"></div>
                    </div>
                </div>
            </div>

            <!-- Mobile: List Tab -->
            <div class="tab-pane fade" id="mobile-list-content" role="tabpanel">
                <div class="card mb-3">
                    <div class="card-body">
                        <div id="mobile-storeList">
                            <!-- Store list will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mobile: Form Tab -->
            <div class="tab-pane fade" id="mobile-form-content" role="tabpanel">
                <div class="card mb-3">
                    <div class="card-header">
                        <h3 id="mobileFormTitle" class="h5 mb-0">إضافة متجر جديد</h3>
                    </div>
                    <div class="card-body">
                        <form id="mobileStoreForm">
                            <input type="hidden" id="mobileStoreId">

                            <div class="mb-3">
                                <label for="mobileStoreName" class="form-label">اسم المتجر</label>
                                <input type="text" class="form-control" id="mobileStoreName" required>
                            </div>

                            <div class="mb-3">
                                <label for="mobileStorePhone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="mobileStorePhone" required>
                            </div>

                            <div class="mb-3">
                                <label for="mobileImageUpload" class="form-label">صورة المتجر</label>
                                <div class="mb-2">
                                    <input type="file" class="form-control" id="mobileImageUpload" accept="image/*">
                                </div>
                                <div id="mobileImagePreview" class="image-preview mb-2 d-none">
                                    <img src="#" alt="معاينة الصورة" class="img-fluid">
                                    <button type="button" class="btn-remove-image">
                                        <i class="fas fa-times-circle"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="mobileCitySelect" class="form-label">المدينة</label>
                                <select class="form-select" id="mobileCitySelect">
                                    <option value="" selected>اختر المدينة</option>
                                    <!-- سيتم ملء هذه القائمة بواسطة JavaScript -->
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="mobileDistrictSelect" class="form-label">المنطقة</label>
                                <select class="form-select" id="mobileDistrictSelect" disabled>
                                    <option value="" selected style="color: #dc3545;">المنطقة غير محددة</option>
                                    <!-- سيتم ملء هذه القائمة بواسطة JavaScript -->
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="mobileStoreFullAddress" class="form-label">عنوان المتجر <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="mobileStoreFullAddress" required>
                            </div>

                            <div class="mb-3">
                                <label for="mobileStoreAddress" class="form-label">وصف المكان</label>
                                <input type="text" class="form-control" id="mobileStoreAddress" placeholder="مثال: بجوار مركز التسوق">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">الموقع</label>
                                <p class="text-muted small">انقر على الخريطة لتحديد الموقع</p>
                                <div class="input-group mb-2">
                                    <input type="text" class="form-control" id="mobileLocationAddress" placeholder="أدخل عنوان الموقع">
                                    <button type="button" class="btn btn-outline-secondary" id="mobileSearchLocation">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                                <p id="mobileSelectedLocation" class="small text-info">لم يتم تحديد موقع</p>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" id="mobileSubmitStore" class="btn btn-primary">
                                    <i class="fas fa-plus-circle me-1"></i> إضافة متجر
                                </button>
                                <div class="d-flex gap-2">
                                    <button type="button" id="mobileEditStore" class="btn btn-outline-primary flex-grow-1 d-none">
                                        <i class="fas fa-edit me-1"></i> تعديل المتجر
                                    </button>
                                    <button type="button" id="mobileClearForm" class="btn btn-outline-secondary flex-grow-1">
                                        <i class="fas fa-times me-1"></i> مسح النموذج
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Desktop layout - will be hidden on small screens -->
        <div class="d-none d-md-block">
            <!-- شريط التبويبات والخريطة في الأعلى -->
            <div class="row mb-4">
                <!-- العمود الأيمن: نموذج إضافة المتجر -->
                <div class="col-md-5">
                    <!-- Store Form -->
                    <div class="card h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h3 id="formTitle" class="h5 mb-0">إضافة متجر جديد</h3>
                            <button type="button" class="btn btn-sm btn-outline-secondary d-md-none" id="toggleFormBtn">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                        <div class="card-body" id="storeFormContainer">
                            <form id="storeForm">
                                <input type="hidden" id="storeId">

                                <div class="row g-2">
                                    <div class="col-md-6 mb-2">
                                        <label for="storeName" class="form-label">اسم المتجر <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="storeName" required>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <label for="storePhone" class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" id="storePhone">
                                    </div>
                                </div>

                                <div class="row g-2">
                                    <div class="col-md-6 mb-2">
                                        <label for="storeListSelect" class="form-label">القائمة</label>
                                        <select class="form-select" id="storeListSelect">
                                            <option value="1" selected>القائمة 1</option>
                                            <option value="2">القائمة 2</option>
                                            <option value="3">القائمة 3</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <label for="storeType" class="form-label">نوع المتجر</label>
                                        <select class="form-select" id="storeType">
                                            <option value="A" selected>A</option>
                                            <option value="B">B</option>
                                            <option value="C">C</option>
                                            <option value="D">D</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="row g-2">
                                    <div class="col-md-6 mb-2">
                                        <label for="citySelect" class="form-label">المدينة</label>
                                        <select class="form-select" id="citySelect">
                                            <option value="" selected>اختر المدينة</option>
                                            <option value="طرابلس">طرابلس</option>
                                            <option value="مصراتة">مصراتة</option>
                                            <option value="بنغازي">بنغازي</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <label for="districtSelect" class="form-label">المنطقة</label>
                                        <select class="form-select" id="districtSelect" disabled>
                                            <option value="" selected style="color: #dc3545;">المنطقة غير محددة</option>
                                            <!-- مناطق طرابلس -->
                                            <optgroup label="مناطق طرابلس" class="tripoli-districts" style="display: none;">
                                                <option value="طرابلس المركز">طرابلس المركز</option>
                                                <option value="المدينة القديمة">المدينة القديمة</option>
                                                <option value="باب البحر">باب البحر</option>
                                                <option value="باب بن غشير">باب بن غشير</option>
                                                <option value="الظهرة">الظهرة</option>
                                                <option value="الظهرة الشرقية">الظهرة الشرقية</option>
                                                <option value="الفلاح">الفلاح</option>
                                                <option value="زناتة الغربية">زناتة الغربية</option>
                                                <option value="باب العزيزية">باب العزيزية</option>
                                                <option value="قرجي">قرجي</option>
                                                <option value="شارع النصر">شارع النصر</option>
                                                <option value="الاندلس">الاندلس</option>
                                                <option value="قرقارش">قرقارش</option>
                                                <option value="السياحية">السياحية</option>
                                                <option value="حي دمشق">حي دمشق</option>
                                                <option value="حي الاندلس">حي الاندلس</option>
                                                <option value="غوط الشعال">غوط الشعال</option>
                                                <option value="حي الكيش">حي الكيش</option>
                                                <option value="السراج">السراج</option>
                                                <option value="سوق الجمعة">سوق الجمعة</option>
                                                <option value="عرادة">عرادة</option>
                                                <option value="الشط">الشط</option>
                                                <option value="البيفي">البيفي</option>
                                                <option value="الشواهدة">الشواهدة</option>
                                                <option value="مطار معيتيقة">مطار معيتيقة</option>
                                                <option value="باب تاجوراء">باب تاجوراء</option>
                                                <option value="أبو سليم">أبو سليم</option>
                                                <option value="المشروع">المشروع (الهضبة الخضراء)</option>
                                                <option value="الهضبة الشرقية">الهضبة الشرقية</option>
                                                <option value="مشروع الهضبة الزراعي">مشروع الهضبة الزراعي</option>
                                                <option value="الخلة">الخلة</option>
                                                <option value="السبعة">السبعة</option>
                                                <option value="تاجوراء">تاجوراء</option>
                                                <option value="تاجوراء الشرقية">تاجوراء الشرقية</option>
                                                <option value="القويعة">القويعة</option>
                                                <option value="النشيع">النشيع</option>
                                                <option value="النخيلة">النخيلة</option>
                                                <option value="الباعيش">الباعيش</option>
                                                <option value="جسر الحشان">جسر الحشان</option>
                                                <option value="سيدي رحومة">سيدي رحومة</option>
                                                <option value="بئر الأسطى ميلاد">بئر الأسطى ميلاد</option>
                                                <option value="جنزور">جنزور</option>
                                                <option value="جنزور الشرقية">جنزور الشرقية</option>
                                                <option value="جنزور الغربية">جنزور الغربية</option>
                                                <option value="الكريمية">الكريمية</option>
                                                <option value="غوط الرمان">غوط الرمان</option>
                                                <option value="كوبري 17">كوبري 17</option>
                                                <option value="النجيلة">النجيلة</option>
                                                <option value="صياد">صياد</option>
                                                <option value="السواني الكبرى">السواني الكبرى</option>
                                                <option value="النجيلة الجنوبية">النجيلة الجنوبية</option>
                                                <option value="قصر بن غشير الشمالية">قصر بن غشير الشمالية</option>
                                                <option value="الكريمية الجنوبية">الكريمية الجنوبية</option>
                                                <option value="الساعدية">الساعدية</option>
                                                <option value="طريق المطار">طريق المطار</option>
                                                <option value="المشروع بسليم">المشروع بسليم</option>
                                                <option value="قصر بن غشير">قصر بن غشير</option>
                                                <option value="المشروع الزراعي">المشروع الزراعي</option>
                                                <option value="سوق الأحد">سوق الأحد</option>
                                                <option value="بئر العالم">بئر العالم</option>
                                                <option value="وادي الربيع">وادي الربيع</option>
                                                <option value="الهيرة">الهيرة</option>
                                                <option value="كُريمية شرقية">كُريمية شرقية</option>
                                                <option value="سيدي السائح">سيدي السائح</option>
                                                <option value="القويعة الجنوبية">القويعة الجنوبية</option>
                                                <option value="السبيعة">السبيعة</option>
                                                <option value="سوق الخميس">سوق الخميس</option>
                                                <option value="الساعدية الشرقية">الساعدية الشرقية</option>
                                                <option value="السبيعة الجنوبية">السبيعة الجنوبية</option>
                                                <option value="مشروع الموز">مشروع الموز</option>
                                            </optgroup>
                                            <!-- مناطق مصراتة -->
                                            <optgroup label="مناطق مصراتة" class="misrata-districts" style="display: none;">
                                                <option value="وسط المدينة (المدينة القديمة)">وسط المدينة (المدينة القديمة)</option>
                                                <option value="المدينة (وسط مصراتة)">المدينة (وسط مصراتة)</option>
                                                <option value="الدافنية">الدافنية</option>
                                                <option value="زاوية المحجوب">زاوية المحجوب</option>
                                                <option value="السواوة">السواوة</option>
                                                <option value="المنطقة الشرقية">المنطقة الشرقية</option>
                                                <option value="الغيران">الغيران</option>
                                                <option value="قصر أحمد">قصر أحمد</option>
                                                <option value="طمينة">طمينة</option>
                                                <option value="الكراريم">الكراريم</option>
                                                <option value="قصر ترهونة">قصر ترهونة</option>
                                                <option value="عين كعام">عين كعام</option>
                                                <option value="الدافنية الشرقية">الدافنية الشرقية</option>
                                                <option value="المنطقة الغربية">المنطقة الغربية</option>
                                                <option value="الدافنية (الغربية)">الدافنية (الغربية)</option>
                                                <option value="زريق">زريق</option>
                                                <option value="زاوية بن زقري">زاوية بن زقري</option>
                                                <option value="الزروق">الزروق</option>
                                                <option value="منطقة 9 يوليو">منطقة 9 يوليو</option>
                                                <option value="المنطقة الجنوبية">المنطقة الجنوبية</option>
                                                <option value="السماردع">السماردع</option>
                                                <option value="القداحية">القداحية</option>
                                                <option value="بئر دوفان">بئر دوفان</option>
                                                <option value="الحامية">الحامية</option>
                                                <option value="أبوقرين">أبوقرين</option>
                                                <option value="المنطقة الشمالية والشاطئية">المنطقة الشمالية والشاطئية</option>
                                                <option value="الشعاب">الشعاب</option>
                                                <option value="زاوية المحجوب الشمالية">زاوية المحجوب الشمالية</option>
                                                <option value="قصر حمد">قصر حمد</option>
                                                <option value="المجمع الصناعي البحري">المجمع الصناعي البحري</option>
                                                <option value="المنطقة الحرة">المنطقة الحرة</option>
                                                <option value="المنطقة الإدارية">المنطقة الإدارية</option>
                                            </optgroup>
                                            <!-- مناطق بنغازي -->
                                            <optgroup label="مناطق بنغازي" class="benghazi-districts" style="display: none;">
                                                <option value="وسط المدينة">وسط المدينة</option>
                                                <option value="البلد">البلد</option>
                                                <option value="المدينة القديمة">المدينة القديمة</option>
                                                <option value="سوق الحوت">سوق الحوت</option>
                                                <option value="الفويهات الشرقية">الفويهات الشرقية</option>
                                                <option value="الصابري">الصابري</option>
                                                <option value="الماجوري">الماجوري</option>
                                                <option value="سيدي حسين">سيدي حسين</option>
                                                <option value="سيدي اخريبيش">سيدي اخريبيش</option>
                                                <option value="البركة">البركة</option>
                                                <option value="المنطقة الشرقية">المنطقة الشرقية</option>
                                                <option value="الليثي">الليثي</option>
                                                <option value="بوهديمة">بوهديمة</option>
                                                <option value="حي السلام">حي السلام</option>
                                                <option value="قاريونس">قاريونس</option>
                                                <option value="الفويهات الغربية">الفويهات الغربية</option>
                                                <option value="الكيش">الكيش</option>
                                                <option value="الهواري">الهواري</option>
                                                <option value="المساكن">المساكن</option>
                                                <option value="المنطقة الغربية">المنطقة الغربية</option>
                                                <option value="بو سليم">بو سليم</option>
                                                <option value="الكويفية">الكويفية</option>
                                                <option value="قنفودة">قنفودة</option>
                                                <option value="القرية السياحية">القرية السياحية</option>
                                                <option value="جليانة">جليانة</option>
                                                <option value="بوعطني">بوعطني</option>
                                                <option value="بنينا">بنينا</option>
                                                <option value="النواقية">النواقية</option>
                                                <option value="سيدي فرج">سيدي فرج</option>
                                                <option value="المنطقة الجنوبية">المنطقة الجنوبية</option>
                                                <option value="طريق المطار">طريق المطار</option>
                                                <option value="السرتي">السرتي</option>
                                                <option value="طابلينو">طابلينو</option>
                                                <option value="المنارة">المنارة</option>
                                                <option value="المساكن الشعبية">المساكن الشعبية</option>
                                                <option value="حي دبي">حي دبي</option>
                                                <option value="حي الحدائق">حي الحدائق</option>
                                                <option value="شبنة">شبنة</option>
                                                <option value="بوزغيبة">بوزغيبة</option>
                                                <option value="حي النسيم">حي النسيم</option>
                                                <option value="أرض شبنة">أرض شبنة</option>
                                                <option value="المساكن الجاهزة">المساكن الجاهزة</option>
                                                <option value="المنطقة الشمالية والشاطئية">المنطقة الشمالية والشاطئية</option>
                                                <option value="رأس أعبيدة">رأس أعبيدة</option>
                                                <option value="الصابري الساحلي">الصابري الساحلي</option>
                                                <option value="سيدي يونس">سيدي يونس</option>
                                                <option value="الليثي البحري">الليثي البحري</option>
                                                <option value="النواقية الساحلية">النواقية الساحلية</option>
                                            </optgroup>
                                        </select>
                                    </div>
                                </div>
                                <div class="row g-2">
                                    <div class="col-md-6 mb-2">
                                        <label for="storeFullAddress" class="form-label">عنوان المتجر <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="storeFullAddress" required>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <label for="storeAddress" class="form-label">وصف المكان</label>
                                        <input type="text" class="form-control" id="storeAddress" placeholder="مثال: بجوار مركز التسوق">
                                    </div>
                                </div>
                                <div class="row g-2">
                                    <div class="col-md-6 mb-2">
                                        <label for="imageUpload" class="form-label">صورة المتجر</label>
                                        <input type="file" class="form-control" id="imageUpload" accept="image/*">
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <!-- مساحة احتياطية للتوازن -->
                                    </div>
                                </div>

                                <div class="mb-2">
                                    <div id="imagePreview" class="image-preview mb-2 d-none">
                                        <img src="#" alt="معاينة الصورة" class="img-fluid">
                                        <button type="button" class="btn-remove-image">
                                            <i class="fas fa-times-circle"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="mb-2">
                                    <label class="form-label">الموقع <span class="text-danger">*</span></label>
                                    <p class="text-muted small mb-1">انقر على الخريطة لتحديد الموقع</p>
                                    <div class="input-group mb-2">
                                        <input type="text" class="form-control" id="locationAddress" placeholder="أدخل عنوان الموقع">
                                        <button type="button" class="btn btn-outline-secondary" id="searchLocation">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                    <p id="selectedLocation" class="small text-info">لم يتم تحديد موقع</p>
                                </div>

                                <div class="d-flex flex-column flex-md-row gap-2 mt-3">
                                    <button type="submit" id="submitStore" class="btn btn-primary flex-grow-1 order-2 order-md-1">
                                        <i class="fas fa-plus-circle me-1"></i> إضافة متجر
                                    </button>
                                    <button type="button" id="viewStoreList" class="btn btn-info flex-grow-1 order-1 order-md-2">
                                        <i class="fas fa-list me-1"></i> عرض القائمة
                                    </button>
                                </div>
                                <div class="d-flex gap-2 mt-2">
                                    <button type="button" id="editStore" class="btn btn-sm btn-outline-primary flex-grow-1 d-none">
                                        <i class="fas fa-edit me-1"></i> تعديل المتجر
                                    </button>
                                    <button type="button" id="clearForm" class="btn btn-sm btn-outline-secondary flex-grow-1">
                                        <i class="fas fa-times me-1"></i> مسح النموذج
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- العمود الأيسر: الخريطة وشريط التبويبات -->
                <div class="col-md-7">
                    <div class="card h-100">
                        <div class="card-header">
                            <ul class="nav nav-tabs card-header-tabs" id="mapListTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="map-tab" data-bs-toggle="tab" data-bs-target="#map-content" type="button" role="tab" aria-controls="map-content" aria-selected="true">الخريطة</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="list-tab-direct" data-bs-toggle="tab" data-bs-target="#list-content" type="button" role="tab" aria-controls="list-content">قائمة المتاجر</button>
                                </li>
                                <!-- قائمة مخفية للاحتفاظ بالأزرار القديمة للتوافق -->
                                <div class="d-none">
                                    <ul class="dropdown-menu" id="list-dropdown-menu">
                                        <li><button class="dropdown-item" id="list-tab-1" data-bs-toggle="tab" data-bs-target="#list-content" data-list-id="1" type="button" role="tab">القائمة 1</button></li>
                                        <li><button class="dropdown-item" id="list-tab-2" data-bs-toggle="tab" data-bs-target="#list-content" data-list-id="2" type="button" role="tab">القائمة 2</button></li>
                                        <li><button class="dropdown-item" id="list-tab-3" data-bs-toggle="tab" data-bs-target="#list-content" data-list-id="3" type="button" role="tab">القائمة 3</button></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><button class="dropdown-item" id="list-tab-all" data-bs-toggle="tab" data-bs-target="#list-content" type="button" role="tab">جميع القوائم</button></li>
                                    </ul>
                                </div>
                            </ul>
                        </div>
                        <div class="card-body p-0">
                            <div class="tab-content" id="mapListTabContent">
                                <!-- Map Tab -->
                                <div class="tab-pane fade show active" id="map-content" role="tabpanel" aria-labelledby="map-tab">
                                    <div id="map"></div>
                                </div>

                            <!-- List Tab -->
                            <div class="tab-pane fade" id="list-content" role="tabpanel" aria-labelledby="list-tab">
                                <div class="card-body p-0">
                                    <!-- شريط البحث والأدوات -->
                                    <div class="store-list-header p-3 border-bottom">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <div class="d-flex align-items-center flex-wrap">
                                                <h5 class="mb-0 me-2">Loacker</h5>
                                                <div class="btn-group btn-group-sm ms-2 mt-1 mt-sm-0">
                                                    <button type="button" id="view-all-lists" class="btn btn-outline-primary btn-sm">
                                                        <i class="fas fa-list-ul d-inline-block d-sm-none"></i>
                                                        <span class="d-none d-sm-inline-block"><i class="fas fa-list-ul me-1"></i> جميع القوائم</span>
                                                    </button>
                                                    <button type="button" class="btn btn-outline-primary btn-sm dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="fas fa-chevron-down"></i>
                                                        <span class="visually-hidden">القوائم</span>
                                                    </button>
                                                    <ul class="dropdown-menu" id="quick-list-menu">
                                                        <!-- سيتم ملؤها بواسطة JavaScript -->
                                                    </ul>
                                                </div>
                                                <!-- زر تحديد الكل - يظهر فقط عند تحديد متجر -->
                                                <button type="button" id="selectAll" class="btn btn-outline-primary btn-sm ms-2 d-none">
                                                    <i class="fas fa-check-double me-1"></i> تحديد الكل
                                                </button>
                                                <!-- زر إلغاء التحديد - يظهر فقط عند تحديد متجر -->
                                                <button type="button" id="cancelSelection" class="btn btn-outline-secondary btn-sm ms-2 d-none">
                                                    <i class="fas fa-times me-1"></i> إلغاء التحديد
                                                </button>

                                            </div>
                                            <span class="badge bg-primary" id="storeCount">0</span>
                                        </div>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="searchInput" placeholder="البحث بالاسم أو رقم الهاتف">
                                            <button class="btn btn-outline-primary" type="button" id="searchButton">
                                                <i class="fas fa-search"></i>
                                            </button>
                                            <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- قائمة المتاجر مع شريط تمرير مستقل -->
                                    <div class="store-list-container">
                                        <div id="storeList" class="store-list-scrollable">
                                            <!-- Store list will be populated by JavaScript -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% else %}
        <!-- محتوى للمستخدمين غير المسجلين -->
        <div class="row">
            <div class="col-md-8 offset-md-2">
                <div class="card">
                    <div class="card-body text-center">
                        <h2 class="mb-4">مرحباً بك في نظام Loacker لإدارة المتاجر</h2>
                        <p class="lead">هذا النظام يتيح لك إدارة مواقع المتاجر على الخريطة بكل سهولة</p>
                        <div class="mt-4">
                            <a href="{{ url_for('login') }}" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i> تسجيل الدخول للبدء
                            </a>
                        </div>
                        <div class="mt-4">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <i class="fas fa-map-marked-alt text-primary mb-3" style="font-size: 2rem;"></i>
                                            <h5>عرض المتاجر على الخريطة</h5>
                                            <p class="small text-muted">يمكنك عرض جميع المتاجر على خريطة تفاعلية</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <i class="fas fa-plus-circle text-success mb-3" style="font-size: 2rem;"></i>
                                            <h5>إضافة متاجر جديدة</h5>
                                            <p class="small text-muted">أضف متاجر جديدة بسهولة مع تحديد موقعها على الخريطة</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <i class="fas fa-users-cog text-warning mb-3" style="font-size: 2rem;"></i>
                                            <h5>إدارة المستخدمين</h5>
                                            <p class="small text-muted">تحكم في صلاحيات المستخدمين وإدارة حساباتهم</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- نافذة حوار التأكيد على الحذف -->
        <div class="modal fade" id="deleteConfirmationModal" tabindex="-1" aria-labelledby="deleteConfirmationModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="deleteConfirmationModalLabel">تأكيد الحذف</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                    </div>
                    <div class="modal-body">
                        <p>هل أنت متأكد من رغبتك في حذف المتجر/المتاجر المحددة؟ لا يمكن التراجع عن هذا الإجراء.</p>
                        <p id="deleteConfirmationCount"></p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-danger" id="confirmDelete">تأكيد الحذف</button>
                    </div>
                </div>
            </div>
        </div>

        <footer class="mt-5 pt-3 border-top text-center text-muted">
            <p>Loacker &copy; 2025</p>
        </footer>
    </div>

    <!-- Bootstrap JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Error Handler JS -->
    <script src="{{ url_for('static', filename='js/error-handler.js') }}"></script>

    <!-- Store List Renderer JS -->
    <script src="{{ url_for('static', filename='js/store-list-renderer.js') }}"></script>

    <!-- WhatsApp Sharing JS -->
    <script src="{{ url_for('static', filename='js/whatsapp-share.js') }}"></script>

    <!-- واجهة الهاتف النقال -->
    <div class="mobile-view">
        <!-- الرأس -->
        <div class="mobile-header">
            <div class="mobile-logo-wrapper">
                <div class="logo-circle-small-mobile">
                    <i class="fas fa-store"></i>
                </div>
                <h1 class="mb-0">Loacker</h1>
            </div>
            <div>
                {% if current_user.is_authenticated %}
                <div class="d-flex align-items-center gap-2">
                    <button type="button" class="btn btn-sm btn-light rounded-circle shadow-sm" id="mobile-user-menu" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-user"></i>
                    </button>
                    <div class="dropdown-menu dropdown-menu-end" aria-labelledby="mobile-user-menu">
                        <a href="{{ url_for('profile') }}" class="dropdown-item">
                            <i class="fas fa-user-circle me-2"></i> الملف الشخصي
                        </a>
                        {% if current_user.role_id == 1 %}
                        <a href="{{ url_for('admin_panel') }}" class="dropdown-item position-relative">
                            <i class="fas fa-cog me-2"></i> لوحة التحكم
                        </a>
                        <a href="{{ url_for('statistics') }}" class="dropdown-item position-relative">
                            <i class="fas fa-chart-pie me-2"></i> الإحصائيات
                        </a>
                        <a href="{{ url_for('admin_panel') }}#pendingStores" class="dropdown-item position-relative">
                            <i class="fas fa-bell me-2"></i> الإشعارات
                            <span id="mobilePendingStoresBadgeBell" class="notification-badge-mobile d-none">0</span>
                        </a>
                        {% endif %}
                        <div class="dropdown-divider"></div>
                        <a href="{{ url_for('logout') }}" class="dropdown-item text-danger">
                            <i class="fas fa-sign-out-alt me-2"></i> تسجيل الخروج
                        </a>
                    </div>
                    <button type="button" class="btn btn-sm btn-light rounded-circle shadow-sm" id="mobile-list-toggle">
                        <i class="fas fa-list"></i>
                    </button>
                </div>
                {% else %}
                <a href="{{ url_for('login') }}" class="btn btn-sm btn-outline-danger">
                    <i class="fas fa-sign-in-alt me-1"></i> تسجيل الدخول
                </a>
                {% endif %}
            </div>
        </div>

        <!-- المحتوى -->
        <div class="mobile-content">
            {% if current_user.is_authenticated %}
            <!-- محتوى التبويبات -->
            <div class="mobile-tab-content active" id="mobile-map-tab">
                <div id="mobile-map" class="mobile-map"></div>
                <div class="d-flex justify-content-center mt-2 mb-3">
                    <button id="mobile-get-location" class="btn btn-sm btn-outline-primary rounded-pill px-3">
                        <i class="fas fa-location-arrow me-1"></i> موقعي الحالي
                    </button>
                </div>
            </div>

            <div class="mobile-tab-content" id="mobile-list-tab">
                <div class="mobile-search">
                    <div class="input-group">
                        <input type="text" class="form-control" id="mobile-search-input" placeholder="البحث عن متجر...">
                        <button class="btn btn-outline-primary" type="button" id="mobile-search-button">
                            <i class="fas fa-search"></i>
                        </button>
                        <button class="btn btn-outline-secondary" type="button" id="mobile-clear-search">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-2 px-1">
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-primary active" id="mobile-all-lists">جميع القوائم</button>
                        <button type="button" class="btn btn-outline-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown">
                            <i class="fas fa-chevron-down"></i>
                            <span class="visually-hidden">القوائم</span>
                        </button>
                        <ul class="dropdown-menu" id="mobile-list-menu">
                            <!-- سيتم ملؤها بواسطة JavaScript -->
                        </ul>
                    </div>
                    <span class="badge bg-primary" id="mobile-store-count">0</span>
                </div>
                <div id="mobile-store-list" class="mobile-store-list">
                    <!-- سيتم ملؤها بواسطة JavaScript -->
                </div>
            </div>

            <div class="mobile-tab-content" id="mobile-form-tab">
                <form id="mobile-store-form" class="mobile-form">
                    <input type="hidden" id="mobile-store-id">
                    <div class="mb-3">
                        <label for="mobile-store-name" class="form-label">اسم المتجر <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="mobile-store-name" required>
                    </div>
                    <div class="mb-3">
                        <label for="mobile-store-phone" class="form-label">رقم الهاتف</label>
                        <input type="tel" class="form-control" id="mobile-store-phone">
                    </div>
                    <div class="mb-3">
                        <label for="mobile-store-list" class="form-label">القائمة</label>
                        <select class="form-select" id="mobile-store-list">
                            <option value="1" selected>القائمة 1</option>
                            <option value="2">القائمة 2</option>
                            <option value="3">القائمة 3</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="mobile-store-type" class="form-label">نوع المتجر</label>
                        <select class="form-select" id="mobile-store-type">
                            <option value="A" selected>A</option>
                            <option value="B">B</option>
                            <option value="C">C</option>
                            <option value="D">D</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="mobile-city-select" class="form-label">المدينة</label>
                        <select class="form-select" id="mobile-city-select">
                            <option value="" selected>اختر المدينة</option>
                            <option value="طرابلس">طرابلس</option>
                            <option value="مصراتة">مصراتة</option>
                            <option value="بنغازي">بنغازي</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="mobile-district-select" class="form-label">المنطقة</label>
                        <select class="form-select" id="mobile-district-select" disabled>
                            <option value="" selected style="color: #dc3545;">المنطقة غير محددة</option>
                            <!-- مناطق طرابلس -->
                            <optgroup label="مناطق طرابلس" class="tripoli-districts">
                                <option value="طرابلس المركز">طرابلس المركز</option>
                                <option value="المدينة القديمة">المدينة القديمة</option>
                                <option value="باب البحر">باب البحر</option>
                                <option value="باب بن غشير">باب بن غشير</option>
                                <option value="الظهرة">الظهرة</option>
                                <option value="الظهرة الشرقية">الظهرة الشرقية</option>
                                <option value="الفلاح">الفلاح</option>
                                <option value="زناتة الغربية">زناتة الغربية</option>
                                <option value="باب العزيزية">باب العزيزية</option>
                                <option value="قرجي">قرجي</option>
                                <option value="شارع النصر">شارع النصر</option>
                                <option value="الاندلس">الاندلس</option>
                                <option value="قرقارش">قرقارش</option>
                                <option value="السياحية">السياحية</option>
                                <option value="حي دمشق">حي دمشق</option>
                                <option value="حي الاندلس">حي الاندلس</option>
                                <option value="غوط الشعال">غوط الشعال</option>
                                <option value="حي الكيش">حي الكيش</option>
                                <option value="السراج">السراج</option>
                                <option value="سوق الجمعة">سوق الجمعة</option>
                                <option value="عرادة">عرادة</option>
                                <option value="الشط">الشط</option>
                                <option value="البيفي">البيفي</option>
                                <option value="الشواهدة">الشواهدة</option>
                                <option value="مطار معيتيقة">مطار معيتيقة</option>
                                <option value="باب تاجوراء">باب تاجوراء</option>
                                <option value="أبو سليم">أبو سليم</option>
                                <option value="المشروع">المشروع (الهضبة الخضراء)</option>
                                <option value="الهضبة الشرقية">الهضبة الشرقية</option>
                                <option value="مشروع الهضبة الزراعي">مشروع الهضبة الزراعي</option>
                                <option value="الخلة">الخلة</option>
                                <option value="السبعة">السبعة</option>
                                <option value="تاجوراء">تاجوراء</option>
                                <option value="تاجوراء الشرقية">تاجوراء الشرقية</option>
                                <option value="القويعة">القويعة</option>
                                <option value="النشيع">النشيع</option>
                                <option value="النخيلة">النخيلة</option>
                                <option value="الباعيش">الباعيش</option>
                                <option value="جسر الحشان">جسر الحشان</option>
                                <option value="سيدي رحومة">سيدي رحومة</option>
                                <option value="بئر الأسطى ميلاد">بئر الأسطى ميلاد</option>
                                <option value="جنزور">جنزور</option>
                                <option value="جنزور الشرقية">جنزور الشرقية</option>
                                <option value="جنزور الغربية">جنزور الغربية</option>
                                <option value="الكريمية">الكريمية</option>
                                <option value="غوط الرمان">غوط الرمان</option>
                                <option value="كوبري 17">كوبري 17</option>
                                <option value="النجيلة">النجيلة</option>
                                <option value="صياد">صياد</option>
                                <option value="السواني الكبرى">السواني الكبرى</option>
                                <option value="النجيلة الجنوبية">النجيلة الجنوبية</option>
                                <option value="قصر بن غشير الشمالية">قصر بن غشير الشمالية</option>
                                <option value="الكريمية الجنوبية">الكريمية الجنوبية</option>
                                <option value="الساعدية">الساعدية</option>
                                <option value="طريق المطار">طريق المطار</option>
                                <option value="المشروع بسليم">المشروع بسليم</option>
                                <option value="قصر بن غشير">قصر بن غشير</option>
                                <option value="المشروع الزراعي">المشروع الزراعي</option>
                                <option value="سوق الأحد">سوق الأحد</option>
                                <option value="بئر العالم">بئر العالم</option>
                                <option value="وادي الربيع">وادي الربيع</option>
                                <option value="الهيرة">الهيرة</option>
                                <option value="كُريمية شرقية">كُريمية شرقية</option>
                                <option value="سيدي السائح">سيدي السائح</option>
                                <option value="القويعة الجنوبية">القويعة الجنوبية</option>
                                <option value="السبيعة">السبيعة</option>
                                <option value="سوق الخميس">سوق الخميس</option>
                                <option value="الساعدية الشرقية">الساعدية الشرقية</option>
                                <option value="السبيعة الجنوبية">السبيعة الجنوبية</option>
                                <option value="مشروع الموز">مشروع الموز</option>
                            </optgroup>
                            <!-- مناطق مصراتة -->
                            <optgroup label="مناطق مصراتة" class="misrata-districts">
                                <option value="وسط المدينة (المدينة القديمة)">وسط المدينة (المدينة القديمة)</option>
                                <option value="المدينة (وسط مصراتة)">المدينة (وسط مصراتة)</option>
                                <option value="الدافنية">الدافنية</option>
                                <option value="زاوية المحجوب">زاوية المحجوب</option>
                                <option value="السواوة">السواوة</option>
                                <option value="المنطقة الشرقية">المنطقة الشرقية</option>
                                <option value="الغيران">الغيران</option>
                                <option value="قصر أحمد">قصر أحمد</option>
                                <option value="طمينة">طمينة</option>
                                <option value="الكراريم">الكراريم</option>
                                <option value="قصر ترهونة">قصر ترهونة</option>
                                <option value="عين كعام">عين كعام</option>
                                <option value="الدافنية الشرقية">الدافنية الشرقية</option>
                                <option value="المنطقة الغربية">المنطقة الغربية</option>
                                <option value="الدافنية (الغربية)">الدافنية (الغربية)</option>
                                <option value="زريق">زريق</option>
                                <option value="زاوية بن زقري">زاوية بن زقري</option>
                                <option value="الزروق">الزروق</option>
                                <option value="منطقة 9 يوليو">منطقة 9 يوليو</option>
                                <option value="المنطقة الجنوبية">المنطقة الجنوبية</option>
                                <option value="السماردع">السماردع</option>
                                <option value="القداحية">القداحية</option>
                                <option value="بئر دوفان">بئر دوفان</option>
                                <option value="الحامية">الحامية</option>
                                <option value="أبوقرين">أبوقرين</option>
                                <option value="المنطقة الشمالية والشاطئية">المنطقة الشمالية والشاطئية</option>
                                <option value="الشعاب">الشعاب</option>
                                <option value="زاوية المحجوب الشمالية">زاوية المحجوب الشمالية</option>
                                <option value="قصر حمد">قصر حمد</option>
                                <option value="المجمع الصناعي البحري">المجمع الصناعي البحري</option>
                                <option value="المنطقة الحرة">المنطقة الحرة</option>
                                <option value="المنطقة الإدارية">المنطقة الإدارية</option>
                            </optgroup>
                            <!-- مناطق بنغازي -->
                            <optgroup label="مناطق بنغازي" class="benghazi-districts">
                                <option value="وسط المدينة">وسط المدينة</option>
                                <option value="البلد">البلد</option>
                                <option value="المدينة القديمة">المدينة القديمة</option>
                                <option value="سوق الحوت">سوق الحوت</option>
                                <option value="الفويهات الشرقية">الفويهات الشرقية</option>
                                <option value="الصابري">الصابري</option>
                                <option value="الماجوري">الماجوري</option>
                                <option value="سيدي حسين">سيدي حسين</option>
                                <option value="سيدي اخريبيش">سيدي اخريبيش</option>
                                <option value="البركة">البركة</option>
                                <option value="المنطقة الشرقية">المنطقة الشرقية</option>
                                <option value="الليثي">الليثي</option>
                                <option value="بوهديمة">بوهديمة</option>
                                <option value="حي السلام">حي السلام</option>
                                <option value="قاريونس">قاريونس</option>
                                <option value="الفويهات الغربية">الفويهات الغربية</option>
                                <option value="الكيش">الكيش</option>
                                <option value="الهواري">الهواري</option>
                                <option value="المساكن">المساكن</option>
                                <option value="المنطقة الغربية">المنطقة الغربية</option>
                                <option value="بو سليم">بو سليم</option>
                                <option value="الكويفية">الكويفية</option>
                                <option value="قنفودة">قنفودة</option>
                                <option value="القرية السياحية">القرية السياحية</option>
                                <option value="جليانة">جليانة</option>
                                <option value="بوعطني">بوعطني</option>
                                <option value="بنينا">بنينا</option>
                                <option value="النواقية">النواقية</option>
                                <option value="سيدي فرج">سيدي فرج</option>
                                <option value="المنطقة الجنوبية">المنطقة الجنوبية</option>
                                <option value="طريق المطار">طريق المطار</option>
                                <option value="السرتي">السرتي</option>
                                <option value="طابلينو">طابلينو</option>
                                <option value="المنارة">المنارة</option>
                                <option value="المساكن الشعبية">المساكن الشعبية</option>
                                <option value="حي دبي">حي دبي</option>
                                <option value="حي الحدائق">حي الحدائق</option>
                                <option value="شبنة">شبنة</option>
                                <option value="بوزغيبة">بوزغيبة</option>
                                <option value="حي النسيم">حي النسيم</option>
                                <option value="أرض شبنة">أرض شبنة</option>
                                <option value="المساكن الجاهزة">المساكن الجاهزة</option>
                                <option value="المنطقة الشمالية والشاطئية">المنطقة الشمالية والشاطئية</option>
                                <option value="رأس أعبيدة">رأس أعبيدة</option>
                                <option value="الصابري الساحلي">الصابري الساحلي</option>
                                <option value="سيدي يونس">سيدي يونس</option>
                                <option value="الليثي البحري">الليثي البحري</option>
                                <option value="النواقية الساحلية">النواقية الساحلية</option>
                            </optgroup>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="mobile-store-full-address" class="form-label">عنوان المتجر</label>
                        <input type="text" class="form-control" id="mobile-store-full-address" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="mobile-store-address" class="form-label">وصف المكان</label>
                        <input type="text" class="form-control" id="mobile-store-address" placeholder="مثال: بجوار مركز التسوق">
                    </div>
                    <div class="mb-3">
                        <label for="mobile-store-image" class="form-label">صورة المتجر</label>
                        <input type="file" class="form-control" id="mobile-store-image" accept="image/*">
                    </div>
                    <div id="mobile-image-preview" class="mb-3 d-none">
                        <img src="#" alt="معاينة الصورة" class="img-fluid rounded">
                        <button type="button" class="btn btn-sm btn-danger mt-2" id="mobile-remove-image">إزالة الصورة</button>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الموقع <span class="text-danger">*</span></label>
                        <p class="text-muted small">انقر على الخريطة لتحديد الموقع</p>
                        <div class="input-group mb-2">
                            <input type="text" class="form-control" id="mobile-location-address" placeholder="أدخل عنوان الموقع">
                            <button type="button" class="btn btn-outline-secondary" id="mobile-search-location">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <p id="mobile-selected-location" class="small text-info">لم يتم تحديد موقع</p>
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary" id="mobile-submit-store">
                            <i class="fas fa-save me-1"></i> حفظ المتجر
                        </button>
                        <div class="d-flex gap-2 mt-2">
                            <button type="button" id="mobile-edit-store" class="btn btn-outline-primary flex-grow-1 d-none">
                                <i class="fas fa-edit me-1"></i> تعديل المتجر
                            </button>
                            <button type="button" id="mobile-clear-form" class="btn btn-outline-secondary flex-grow-1">
                                <i class="fas fa-times me-1"></i> مسح النموذج
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- زر إضافة متجر جديد -->
            <button type="button" class="mobile-fab" id="mobile-add-store">
                <i class="fas fa-plus"></i>
            </button>
            {% else %}
            <!-- محتوى للمستخدمين غير المسجلين -->
            <div class="p-3">
                <div class="card shadow-sm border-0 rounded-4 mb-4">
                    <div class="card-body text-center p-4">
                        <div class="mb-3">
                            <div class="logo-circle mx-auto mb-3" style="width: 80px; height: 80px;">
                                <i class="fas fa-store" style="font-size: 2.5rem;"></i>
                            </div>
                            <h2 class="mb-2">مرحباً بك في Loacker</h2>
                            <p class="text-muted">نظام إدارة مواقع المتاجر على الخريطة</p>
                        </div>
                        <a href="{{ url_for('login') }}" class="btn btn-danger btn-lg w-100 mb-3">
                            <i class="fas fa-sign-in-alt me-2"></i> تسجيل الدخول
                        </a>
                        <p class="small text-muted mb-0">بيانات تسجيل الدخول الافتراضية: <strong>admin / admin123</strong></p>
                    </div>
                </div>

                <div class="row g-3">
                    <div class="col-12">
                        <div class="card shadow-sm border-0 rounded-4">
                            <div class="card-body p-3">
                                <div class="d-flex align-items-center">
                                    <div class="feature-icon bg-danger bg-opacity-10 text-danger rounded-3 p-3 me-3">
                                        <i class="fas fa-map-marked-alt"></i>
                                    </div>
                                    <div>
                                        <h5 class="mb-1">عرض المتاجر على الخريطة</h5>
                                        <p class="text-muted small mb-0">يمكنك عرض جميع المتاجر على خريطة تفاعلية</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="card shadow-sm border-0 rounded-4">
                            <div class="card-body p-3">
                                <div class="d-flex align-items-center">
                                    <div class="feature-icon bg-success bg-opacity-10 text-success rounded-3 p-3 me-3">
                                        <i class="fas fa-plus-circle"></i>
                                    </div>
                                    <div>
                                        <h5 class="mb-1">إضافة متاجر جديدة</h5>
                                        <p class="text-muted small mb-0">أضف متاجر جديدة بسهولة مع تحديد موقعها</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="card shadow-sm border-0 rounded-4">
                            <div class="card-body p-3">
                                <div class="d-flex align-items-center">
                                    <div class="feature-icon bg-primary bg-opacity-10 text-primary rounded-3 p-3 me-3">
                                        <i class="fas fa-users-cog"></i>
                                    </div>
                                    <div>
                                        <h5 class="mb-1">إدارة المستخدمين</h5>
                                        <p class="text-muted small mb-0">تحكم في صلاحيات المستخدمين وإدارة حساباتهم</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        {% if current_user.is_authenticated %}
        <!-- شريط التبويبات السفلي -->
        <div class="mobile-tabs">
            <button type="button" class="mobile-tab-btn active" data-tab="mobile-map-tab">
                <i class="fas fa-map-marked-alt"></i>
                <span>الخريطة</span>
            </button>
            <button type="button" class="mobile-tab-btn" data-tab="mobile-list-tab">
                <i class="fas fa-list"></i>
                <span>المتاجر</span>
            </button>
            <button type="button" class="mobile-tab-btn" data-tab="mobile-form-tab">
                <i class="fas fa-plus-circle"></i>
                <span>إضافة</span>
            </button>
        </div>
        {% endif %}
    </div>

    <!-- نافذة عرض الصورة المكبرة -->
    <div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="imageModalLabel">صورة المتجر</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="" alt="صورة المتجر" class="img-fluid rounded">
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة تأكيد الحذف الجماعي -->
    <div class="modal fade" id="deleteConfirmationModal" tabindex="-1" aria-labelledby="deleteConfirmationModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteConfirmationModalLabel">تأكيد الحذف</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-3">
                        <i class="fas fa-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                    </div>
                    <p class="text-center fs-5">هل أنت متأكد من رغبتك في حذف المتاجر المحددة؟</p>
                    <p class="text-center text-danger" id="deleteConfirmationCount">عدد المتاجر المحددة للحذف: 0</p>
                    <p class="text-center text-muted">لا يمكن التراجع عن هذا الإجراء.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-danger" id="confirmDelete">
                        <i class="fas fa-trash me-1"></i> تأكيد الحذف
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة تأكيد تغيير موقع المتجر -->
    <div class="modal fade" id="locationChangeModal" tabindex="-1" aria-labelledby="locationChangeModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="locationChangeModalLabel">تأكيد تغيير الموقع</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-3">
                        <i class="fas fa-map-marker-alt text-primary" style="font-size: 3rem;"></i>
                    </div>
                    <p class="text-center fs-5">لقد قمت بتغيير موقع المتجر على الخريطة.</p>
                    <p class="text-center">هل تريد حفظ الموقع الجديد، أم الاحتفاظ بالموقع الأصلي؟</p>
                    <div id="locationChangeStatus" class="alert alert-info text-center d-none">
                        <i class="fas fa-spinner fa-spin me-2"></i> جاري حفظ التغييرات...
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" id="keepOriginalLocation">الاحتفاظ بالموقع الأصلي</button>
                    <button type="button" class="btn btn-primary" id="confirmLocationChange">
                        <i class="fas fa-check me-1"></i> تأكيد الموقع الجديد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>

    <!-- Application JS -->
    <!-- كاشف نوع الجهاز - يجب تحميله أولاً -->
    <script src="{{ url_for('static', filename='js/device-detector.js') }}"></script>
    <script src="{{ url_for('static', filename='js/map.js') }}"></script>
    <script src="{{ url_for('static', filename='js/store.js') }}"></script>
    <script src="{{ url_for('static', filename='js/updateStore.js') }}"></script>
    <!-- تحميل بيانات المدن والمناطق من قاعدة البيانات -->
    <script src="{{ url_for('static', filename='js/regions-loader.js') }}"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
    <script src="{{ url_for('static', filename='js/mobile.js') }}"></script>
    <!-- إضافة ملف البحث في قائمة المناطق المنسدلة -->
    <script src="{{ url_for('static', filename='js/district-search.js') }}"></script>
    <!-- مدير الصلاحيات - يجب تحميله بعد جميع ملفات JavaScript الأخرى -->
    <script src="{{ url_for('static', filename='js/permission-manager.js') }}"></script>
    <!-- نظام إشعارات المتاجر المعلقة -->
    <script src="{{ url_for('static', filename='js/pending-stores-notifier.js') }}"></script>
    <!-- أداة تشخيص واجهة الهاتف (للتطوير فقط) -->
    <script src="{{ url_for('static', filename='js/mobile-debug.js') }}"></script>
</body>
</html>
