/**
 * DeviceDetector - فئة محسنة للكشف عن نوع الجهاز وحجم الشاشة وتطبيق الواجهة المناسبة
 * تدعم جميع أنواع الهواتف المحمولة بما في ذلك iPhone (7, 8, 11) وأجهزة Xiaomi وغيرها
 * Created: 2024
 */
class DeviceDetector {
    /**
     * إنشاء كائن جديد من كاشف الأجهزة
     * @param {Object} options - خيارات التهيئة
     */
    constructor(options = {}) {
        // الخيارات الافتراضية
        this.options = {
            mobileBreakpoint: 768, // نقطة الفصل بين الأجهزة المحمولة والكمبيوتر (بالبكسل)
            tabletBreakpoint: 1024, // نقطة الفصل بين الأجهزة اللوحية والكمبيوتر (بالبكسل)
            smallPhoneBreakpoint: 375, // نقطة الفصل للهواتف الصغيرة مثل iPhone 7/8
            applyClasses: true, // تطبيق فئات CSS تلقائيًا
            forceDetection: false, // إجبار الكشف عن الأجهزة المحمولة حتى لو كان حجم الشاشة كبيرًا
            ...options
        };

        // حالة الجهاز
        this.isMobile = false;
        this.isTablet = false;
        this.isDesktop = true;
        this.isSmallPhone = false; // للهواتف الصغيرة مثل iPhone 7/8
        this.isLargePhone = false; // للهواتف الكبيرة مثل iPhone 11/Pro Max
        this.deviceType = 'unknown';
        this.deviceModel = 'unknown';
        this.screenWidth = window.innerWidth;
        this.screenHeight = window.innerHeight;
        this.pixelRatio = window.devicePixelRatio || 1;
        this.orientation = this.getOrientation();
        this.userAgent = navigator.userAgent || navigator.vendor || window.opera;

        // الكلمات المفتاحية للأجهزة المحمولة
        this.mobileKeywords = [
            'Android', 'webOS', 'iPhone', 'iPad', 'iPod', 'BlackBerry',
            'IEMobile', 'Opera Mini', 'Mobile', 'mobile', 'Tablet', 'tablet',
            'Mi', 'Redmi', 'POCO', 'Xiaomi', 'SM-', 'Samsung', 'Huawei', 'Honor',
            'OPPO', 'vivo', 'Realme', 'OnePlus', 'Nokia', 'LG', 'HTC', 'Lenovo',
            'ZTE', 'Motorola', 'Sony'
        ];

        // تحديد نوع الجهاز عند الإنشاء
        this.checkDeviceType();

        // إضافة مستمع لتغيير حجم النافذة
        this.setupEventListeners();

        console.log(`🔍 DeviceDetector initialized - Device: ${this.getDeviceTypeString()}, Model: ${this.deviceModel}, Screen: ${this.screenWidth}x${this.screenHeight}, Ratio: ${this.pixelRatio}`);
    }

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // مستمع تغيير حجم النافذة
        window.addEventListener('resize', () => {
            clearTimeout(window.resizeTimer);
            window.resizeTimer = setTimeout(() => {
                this.checkDeviceType();
            }, 250);
        });

        // مستمع تغيير اتجاه الشاشة
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.checkDeviceType();
            }, 300);
        });
    }

    /**
     * التحقق من نوع الجهاز وتحديث الحالة
     */
    checkDeviceType() {
        // تحديث أبعاد الشاشة
        this.screenWidth = window.innerWidth;
        this.screenHeight = window.innerHeight;
        this.pixelRatio = window.devicePixelRatio || 1;
        this.orientation = this.getOrientation();
        this.userAgent = navigator.userAgent || navigator.vendor || window.opera; // تحديث userAgent عند كل فحص

        // التحقق من وجود كلمات مفتاحية للأجهزة المحمولة
        const hasMobileKeywords = this.mobileKeywords.some(keyword =>
            this.userAgent.indexOf(keyword) !== -1
        );

        // تحديد نوع الجهاز بناءً على حجم الشاشة والكلمات المفتاحية
        const isSmallScreen = this.screenWidth < this.options.mobileBreakpoint;
        const isMediumScreen = this.screenWidth >= this.options.mobileBreakpoint &&
                              this.screenWidth < this.options.tabletBreakpoint;
        const isVerySmallScreen = this.screenWidth <= this.options.smallPhoneBreakpoint;

        // تحديد نوع الجهاز بناءً على المعلومات المتاحة
        this.detectDeviceModel();

        // تحديث حالة الجهاز
        this.isMobile = isSmallScreen || hasMobileKeywords || this.options.forceDetection;
        this.isTablet = !this.isMobile && (isMediumScreen || this.userAgent.indexOf('iPad') !== -1);
        this.isDesktop = !this.isMobile && !this.isTablet;
        this.isSmallPhone = isVerySmallScreen || this.deviceModel.includes('iPhone 7') || this.deviceModel.includes('iPhone 8');
        this.isLargePhone = this.isMobile && !this.isSmallPhone && (this.deviceModel.includes('iPhone 11') || this.deviceModel.includes('iPhone 12') || this.screenWidth >= 390);

        // تطبيق فئات CSS إذا كان مطلوبًا
        if (this.options.applyClasses) {
            this.applyDeviceClasses();
        }

        return {
            isMobile: this.isMobile,
            isTablet: this.isTablet,
            isDesktop: this.isDesktop,
            isSmallPhone: this.isSmallPhone,
            isLargePhone: this.isLargePhone,
            deviceType: this.deviceType,
            deviceModel: this.deviceModel,
            screenWidth: this.screenWidth,
            screenHeight: this.screenHeight,
            pixelRatio: this.pixelRatio,
            orientation: this.orientation
        };
    }

    /**
     * محاولة تحديد طراز الجهاز من سلسلة user agent
     */
    detectDeviceModel() {
        const ua = this.userAgent;

        // تحديد نوع الجهاز الرئيسي
        if (ua.indexOf('iPhone') !== -1) {
            this.deviceType = 'iPhone';

            // محاولة تحديد طراز iPhone بناءً على حجم الشاشة ونسبة البكسل
            if (this.screenWidth === 375 && this.screenHeight === 667 && this.pixelRatio === 2) {
                this.deviceModel = 'iPhone 7/8/SE2';
            } else if (this.screenWidth === 414 && this.screenHeight === 736 && this.pixelRatio === 3) {
                this.deviceModel = 'iPhone 7/8 Plus';
            } else if (this.screenWidth === 375 && this.screenHeight === 812 && this.pixelRatio === 3) {
                this.deviceModel = 'iPhone X/XS/11 Pro';
            } else if (this.screenWidth === 414 && this.screenHeight === 896) {
                this.deviceModel = this.pixelRatio === 2 ? 'iPhone XR/11' : 'iPhone XS Max/11 Pro Max';
            } else if (this.screenWidth === 390 && this.screenHeight === 844 && this.pixelRatio === 3) {
                this.deviceModel = 'iPhone 12/12 Pro/13/13 Pro';
            } else if (this.screenWidth === 428 && this.screenHeight === 926 && this.pixelRatio === 3) {
                this.deviceModel = 'iPhone 12/13 Pro Max';
            } else {
                this.deviceModel = 'iPhone (unknown model)';
            }
        } else if (ua.indexOf('iPad') !== -1) {
            this.deviceType = 'iPad';
            this.deviceModel = 'iPad';
        } else if (ua.indexOf('Xiaomi') !== -1 || ua.indexOf('Mi ') !== -1 || ua.indexOf('Redmi') !== -1) {
            this.deviceType = 'Xiaomi';

            // محاولة استخراج طراز Xiaomi
            const xiaomiRegex = /(Xiaomi|Mi|Redmi|POCO)[\s_-]([^;/]+)/i;
            const match = ua.match(xiaomiRegex);
            if (match) {
                this.deviceModel = `${match[1]} ${match[2]}`;
            } else {
                this.deviceModel = 'Xiaomi (unknown model)';
            }
        } else if (ua.indexOf('Samsung') !== -1 || ua.indexOf('SM-') !== -1) {
            this.deviceType = 'Samsung';

            // محاولة استخراج طراز Samsung
            const samsungRegex = /SM-([^;/\)]+)/i;
            const match = ua.match(samsungRegex);
            if (match) {
                this.deviceModel = `Samsung ${match[1]}`;
            } else {
                this.deviceModel = 'Samsung (unknown model)';
            }
        } else if (ua.indexOf('Android') !== -1) {
            this.deviceType = 'Android';
            this.deviceModel = 'Android Device';
        } else if (ua.indexOf('Windows') !== -1) {
            this.deviceType = 'Windows';
            this.deviceModel = 'Windows Device';
        } else if (ua.indexOf('Macintosh') !== -1) {
            this.deviceType = 'Mac';
            this.deviceModel = 'Mac Device';
        } else if (ua.indexOf('Linux') !== -1) {
            this.deviceType = 'Linux';
            this.deviceModel = 'Linux Device';
        } else {
            this.deviceType = 'Unknown';
            this.deviceModel = 'Unknown Device';
        }
    }

    /**
     * الحصول على اتجاه الشاشة (أفقي أو عمودي)
     * @returns {string} - اتجاه الشاشة ('portrait' أو 'landscape')
     */
    getOrientation() {
        return this.screenWidth > this.screenHeight ? 'landscape' : 'portrait';
    }

    /**
     * الحصول على نوع الجهاز كنص
     * @returns {string} - نوع الجهاز (mobile, tablet, desktop)
     */
    getDeviceTypeString() {
        if (this.isMobile) return 'mobile';
        if (this.isTablet) return 'tablet';
        return 'desktop';
    }

    /**
     * تطبيق فئات CSS المناسبة على عنصر body
     */
    applyDeviceClasses() {
        const body = document.body;
        const html = document.documentElement;

        // إزالة جميع فئات الأجهزة
        body.classList.remove(
            'mobile-device', 'tablet-device', 'desktop-device',
            'small-phone', 'large-phone', 'iphone-device', 'xiaomi-device',
            'samsung-device', 'android-device', 'portrait-mode', 'landscape-mode'
        );

        // إضافة فئة الاتجاه
        body.classList.add(this.orientation === 'portrait' ? 'portrait-mode' : 'landscape-mode');

        // إضافة فئة نوع الجهاز الرئيسي
        if (this.isMobile) {
            body.classList.add('mobile-device');

            // إضافة فئات لنوع الهاتف المحدد
            if (this.isSmallPhone) {
                body.classList.add('small-phone');
            } else if (this.isLargePhone) {
                body.classList.add('large-phone');
            }

            // إضافة فئة لنوع الجهاز
            if (this.deviceType === 'iPhone') {
                body.classList.add('iphone-device');
            } else if (this.deviceType === 'Xiaomi') {
                body.classList.add('xiaomi-device');
            } else if (this.deviceType === 'Samsung') {
                body.classList.add('samsung-device');
            } else if (this.deviceType === 'Android') {
                body.classList.add('android-device');
            }

            // إضافة متغيرات CSS للمساعدة في التنسيق
            html.style.setProperty('--device-width', `${this.screenWidth}px`);
            html.style.setProperty('--device-height', `${this.screenHeight}px`);
            html.style.setProperty('--device-pixel-ratio', this.pixelRatio);
            html.style.setProperty('--device-type', `"${this.deviceType}"`);
            html.style.setProperty('--device-model', `"${this.deviceModel}"`);

            this.applyMobileInterface();
        } else if (this.isTablet) {
            body.classList.add('tablet-device');
            this.applyTabletInterface();
        } else {
            body.classList.add('desktop-device');
            this.applyDesktopInterface();
        }

        // إضافة معلومات الجهاز للمساعدة في تصحيح الأخطاء
        console.log(`📱 Device: ${this.deviceType} (${this.deviceModel}), Screen: ${this.screenWidth}x${this.screenHeight}, Ratio: ${this.pixelRatio}, Mode: ${this.orientation}`);
    }

    /**
     * تطبيق واجهة الهاتف المحمول
     */
    applyMobileInterface() {
        const containerFluid = document.querySelector('.container-fluid');
        const mobileView = document.querySelector('.mobile-view');
        const bootstrapMobileContent = document.querySelector('.d-md-none');
        const html = document.documentElement;

        // إخفاء واجهة سطح المكتب
        if (containerFluid) containerFluid.style.display = 'none';

        // إخفاء واجهة Bootstrap المحمولة (التبويبات القديمة)
        if (bootstrapMobileContent) {
            bootstrapMobileContent.style.display = 'none';
        }

        // إظهار واجهة الهاتف المخصصة
        if (mobileView) {
            mobileView.style.display = 'block';
            mobileView.style.visibility = 'visible';

            // إضافة فئات لنوع الهاتف المحدد
            if (this.isSmallPhone) {
                mobileView.classList.add('small-phone-view');
                mobileView.classList.remove('large-phone-view');
            } else {
                mobileView.classList.add('large-phone-view');
                mobileView.classList.remove('small-phone-view');
            }

            // إضافة فئة لنوع الجهاز
            mobileView.classList.remove('iphone-view', 'xiaomi-view', 'samsung-view', 'android-view');
            if (this.deviceType === 'iPhone') {
                mobileView.classList.add('iphone-view');
            } else if (this.deviceType === 'Xiaomi') {
                mobileView.classList.add('xiaomi-view');
            } else if (this.deviceType === 'Samsung') {
                mobileView.classList.add('samsung-view');
            } else if (this.deviceType === 'Android') {
                mobileView.classList.add('android-view');
            }
        }

        // إضافة متغيرات CSS للمساعدة في التنسيق
        html.style.setProperty('--safe-area-inset-top', '0px');
        html.style.setProperty('--safe-area-inset-bottom', '0px');

        // إضافة مناطق آمنة للأجهزة التي تحتوي على notch
        if (this.deviceModel.includes('iPhone X') || this.deviceModel.includes('iPhone 11') || this.deviceModel.includes('iPhone 12') || this.deviceModel.includes('iPhone 13')) {
            html.style.setProperty('--safe-area-inset-top', '44px');
            html.style.setProperty('--safe-area-inset-bottom', '34px');
        }

        // إضافة متغيرات للمساعدة في التنسيق
        if (this.isSmallPhone) {
            html.style.setProperty('--font-size-multiplier', '0.9');
        } else {
            html.style.setProperty('--font-size-multiplier', '1');
        }

        // إضافة متغيرات للمساعدة في التنسيق بناءً على نوع الجهاز
        if (this.deviceType === 'iPhone') {
            html.style.setProperty('--device-font-family', '"SF Pro Text", -apple-system, BlinkMacSystemFont, sans-serif');
        } else if (this.deviceType === 'Xiaomi') {
            html.style.setProperty('--device-font-family', '"Mi Sans", "Roboto", sans-serif');
        } else if (this.deviceType === 'Samsung') {
            html.style.setProperty('--device-font-family', '"Samsung Sans", "Roboto", sans-serif');
        } else {
            html.style.setProperty('--device-font-family', '"Roboto", "Segoe UI", sans-serif');
        }

        console.log(`📱 تم تطبيق واجهة الهاتف المحمول (${this.deviceModel})`);
    }

    /**
     * تطبيق واجهة الجهاز اللوحي
     */
    applyTabletInterface() {
        const containerFluid = document.querySelector('.container-fluid');
        const mobileView = document.querySelector('.mobile-view');
        const bootstrapMobileContent = document.querySelector('.d-md-none');

        // يمكن تخصيص سلوك الأجهزة اللوحية هنا
        // في هذه الحالة نستخدم واجهة سطح المكتب للأجهزة اللوحية
        if (containerFluid) containerFluid.style.display = 'block';
        if (mobileView) mobileView.style.display = 'none';

        // إظهار واجهة Bootstrap المحمولة للأجهزة اللوحية (اختياري)
        if (bootstrapMobileContent) {
            bootstrapMobileContent.style.display = 'block';
        }

        console.log('📱 تم تطبيق واجهة الجهاز اللوحي');
    }

    /**
     * تطبيق واجهة سطح المكتب
     */
    applyDesktopInterface() {
        const containerFluid = document.querySelector('.container-fluid');
        const mobileView = document.querySelector('.mobile-view');
        const bootstrapMobileContent = document.querySelector('.d-md-none');

        if (containerFluid) containerFluid.style.display = 'block';
        if (mobileView) mobileView.style.display = 'none';

        // إخفاء واجهة Bootstrap المحمولة على سطح المكتب
        if (bootstrapMobileContent) {
            bootstrapMobileContent.style.display = 'none';
        }

        console.log('🖥️ تم تطبيق واجهة سطح المكتب');
    }
}

// إنشاء كائن عام من كاشف الأجهزة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    // إنشاء كائن DeviceDetector وجعله متاحًا عالميًا
    window.deviceDetector = new DeviceDetector();

    // إضافة حدث مخصص لإعلام الملفات الأخرى بأن كاشف الأجهزة جاهز
    const deviceReadyEvent = new CustomEvent('deviceDetectorReady', {
        detail: { deviceDetector: window.deviceDetector }
    });
    document.dispatchEvent(deviceReadyEvent);

    console.log('🔧 Device detector ready and event dispatched');
});
