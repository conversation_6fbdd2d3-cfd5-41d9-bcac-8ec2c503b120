/**
 * JavaScript لواجهة الهاتف النقال
 */
// الاستماع لحدث جاهزية كاشف الأجهزة
document.addEventListener('deviceDetectorReady', (event) => {
    console.log('📱 Device detector ready event received');
    initializeMobileInterface();
});

// الاستماع لحدث تحميل الصفحة كبديل
document.addEventListener('DOMContentLoaded', () => {
    // انتظار قليل للتأكد من تحميل device-detector.js
    setTimeout(() => {
        initializeMobileInterface();
    }, 1000);
});

function initializeMobileInterface() {
    // التحقق من وجود واجهة الهاتف النقال والتحقق من أن الجهاز هو هاتف محمول
    const mobileView = document.querySelector('.mobile-view');
    const isMobileDevice = document.body.classList.contains('mobile-device');

    console.log('Mobile view element:', mobileView);
    console.log('Is mobile device:', isMobileDevice);
    console.log('Mobile view display style:', mobileView ? window.getComputedStyle(mobileView).display : 'N/A');

    if (!mobileView || !isMobileDevice) {
        console.log('لا يوجد واجهة هاتف أو الجهاز ليس هاتفًا محمولاً');
        return;
    }

    console.log('Initializing mobile interface for mobile device...');

    // تهيئة الخريطة للهاتف النقال
    let mobileMap = null;
    let mobileSelectedLocation = null;
    let mobileMarkers = [];
    let mobileStores = [];
    let mobileCurrentListId = null;
    let mobileLists = []; // قائمة بالقوائم المخصصة

    // تحميل القوائم المخصصة
    function loadMobileLists() {
        console.log('Loading mobile custom lists...');
        fetch('/api/custom-lists')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    mobileLists = data.lists;
                    console.log('Loaded mobile lists:', mobileLists);

                    // تحديث أسماء القوائم في الواجهة
                    updateListNames();
                }
            })
            .catch(error => {
                console.error('Error loading mobile lists:', error);
            });
    }

    // تحديث أسماء القوائم في الواجهة
    function updateListNames() {
        // تحديث أسماء القوائم في بطاقات المتاجر
        document.querySelectorAll('.list-name').forEach(element => {
            const listId = element.getAttribute('data-list-id');
            const list = mobileLists.find(l => l.id == listId);
            if (list) {
                element.textContent = list.name;
            } else {
                element.textContent = `القائمة ${listId}`;
            }
        });

        // تحديث أسماء القوائم في النوافذ المنبثقة
        document.querySelectorAll('.popup-list-name').forEach(element => {
            const listId = element.getAttribute('data-list-id');
            const list = mobileLists.find(l => l.id == listId);
            if (list) {
                element.textContent = list.name;
            } else {
                element.textContent = `القائمة ${listId}`;
            }
        });
    }

    // تهيئة الخريطة
    function initMobileMap() {
        // التحقق من وجود عنصر الخريطة
        let mapElement = document.getElementById('mobile-map');
        if (!mapElement) {
            console.log('Mobile map element not found');
            return;
        }

        // التحقق من وجود مكتبة Leaflet
        if (typeof L === 'undefined') {
            console.error('Leaflet library not available');
            return;
        }

        console.log('Initializing mobile map...');

        // إذا كانت الخريطة موجودة بالفعل، استخدمها
        if (mobileMap) {
            console.log('Mobile map already exists, reusing it');
            try {
                mobileMap.invalidateSize();
                return mobileMap;
            } catch (e) {
                console.warn('Error reusing existing map:', e);
                // سنستمر في إنشاء خريطة جديدة
            }
        }

        // إزالة أي خريطة موجودة على العنصر
        try {
            // إزالة أي سمات Leaflet من العنصر
            if (mapElement._leaflet_id) {
                console.log('Cleaning up existing map element');

                // إنشاء عنصر div جديد
                const newMapDiv = document.createElement('div');
                newMapDiv.id = 'mobile-map';
                newMapDiv.style.height = '70vh';

                // استبدال العنصر القديم بالعنصر الجديد
                if (mapElement.parentNode) {
                    mapElement.parentNode.replaceChild(newMapDiv, mapElement);
                    console.log('Map element replaced successfully');

                    // تحديث المرجع للعنصر الجديد
                    mapElement = newMapDiv;
                } else {
                    console.warn('Map element has no parent node, cannot replace');
                }
            }
        } catch (e) {
            console.warn('Error cleaning up map element:', e);
        }

        // تأخير قصير قبل إنشاء الخريطة
        setTimeout(() => {
            try {
                console.log('Creating new mobile map');

                // إنشاء خريطة جديدة
                mobileMap = L.map('mobile-map', {
                    center: [32.8872, 13.1913],
                    zoom: 13
                });

                // إضافة طبقة الخريطة
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                }).addTo(mobileMap);

                console.log('Mobile map created successfully');

                // إضافة طبقة القمر الصناعي
                const satelliteLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                    attribution: 'Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community'
                });

                // إضافة طبقة الطرق
                const streetsLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                });

                // إضافة طبقة التضاريس
                const terrainLayer = L.tileLayer('https://stamen-tiles-{s}.a.ssl.fastly.net/terrain/{z}/{x}/{y}{r}.png', {
                    attribution: 'Map tiles by <a href="http://stamen.com">Stamen Design</a>, <a href="http://creativecommons.org/licenses/by/3.0">CC BY 3.0</a> &mdash; Map data &copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                });

                // إنشاء قائمة الطبقات
                const baseMaps = {
                    "الخريطة": streetsLayer,
                    "القمر الصناعي": satelliteLayer,
                    "التضاريس": terrainLayer
                };

                // إضافة تحكم الطبقات
                L.control.layers(baseMaps).addTo(mobileMap);

                // تحديث حجم الخريطة
                setTimeout(() => {
                    mobileMap.invalidateSize();

                    // إعداد مستمعات الأحداث للخريطة بعد التأكد من تهيئة الخريطة
                    setupMapEventListeners();

                    // تحميل البيانات الأولية
                    loadInitialData();
                }, 100);

            } catch (error) {
                console.error('Failed to create mobile map:', error);
            }
        }, 100);
    }

    // تحميل القوائم والمتاجر
    function loadMobileLists() {
        // تنفيذ تحميل القوائم
        console.log("Loading mobile lists...");
        // هنا يمكن إضافة كود لتحميل القوائم من الخادم
    }

    // تهيئة مستمعات الأحداث للخريطة
    function setupMapEventListeners() {
        if (!mobileMap) {
            console.warn("Cannot setup map event listeners: mobileMap is not initialized");
            return;
        }

        console.log("Setting up map event listeners");

        // إضافة مستمع النقر على الخريطة
        mobileMap.on('click', (e) => {
            setMobileSelectedLocation(e.latlng);

            // إغلاق قائمة الطبقات عند النقر على الخريطة
            const layersControl = document.querySelector('#mobile-map .leaflet-control-layers.leaflet-control-layers-expanded');
            if (layersControl) {
                layersControl.classList.remove('leaflet-control-layers-expanded');
            }
        });

        // إضافة ميزة التصغير التلقائي عند تحريك الخريطة
        mobileMap.on('movestart', () => {
            // إغلاق قائمة الطبقات عند تحريك الخريطة
            const layersControl = document.querySelector('#mobile-map .leaflet-control-layers.leaflet-control-layers-expanded');
            if (layersControl) {
                layersControl.classList.remove('leaflet-control-layers-expanded');
            }
        });
    }

    // تحميل البيانات الأولية
    function loadInitialData() {
        loadMobileLists();
        loadMobileStores();
    }

    // تعيين الموقع المحدد
    function setMobileSelectedLocation(latlng) {
        mobileSelectedLocation = latlng;

        // تحديث النص
        const locationText = document.getElementById('mobile-selected-location');
        if (locationText) {
            locationText.textContent = `${latlng.lat.toFixed(6)}, ${latlng.lng.toFixed(6)}`;
            locationText.classList.remove('text-info');
            locationText.classList.add('text-success');
        }

        // إضافة مستمع حدث لقائمة المدن المنسدلة في النموذج الثالث
        const mobileCitySelect = document.getElementById('mobileCitySelect');
        const mobileDistrictSelect = document.getElementById('mobileDistrictSelect');
        const mobileLocationAddress = document.getElementById('mobileLocationAddress');

        // إصلاح مشكلة ظهور القائمة المنسدلة فوق الصندوق في النموذج الثالث
        if (mobileDistrictSelect) {
            mobileDistrictSelect.style.position = 'relative';
            mobileDistrictSelect.style.zIndex = '1000';
        }

        if (mobileCitySelect && mobileLocationAddress) {
            mobileCitySelect.addEventListener('change', function() {
                const selectedCity = this.value;

                // تفعيل/تعطيل قائمة المناطق بناءً على المدينة المختارة
                if (mobileDistrictSelect) {
                    if (selectedCity === 'طرابلس') {
                        // تفعيل قائمة المناطق لمدينة طرابلس
                        mobileDistrictSelect.disabled = false;

                        // إظهار مناطق طرابلس فقط
                        const tripoliDistricts = mobileDistrictSelect.querySelector('.tripoli-districts');
                        const misrataDistricts = mobileDistrictSelect.querySelector('.misrata-districts');
                        const benghaziDistricts = mobileDistrictSelect.querySelector('.benghazi-districts');

                        if (tripoliDistricts) tripoliDistricts.style.display = 'block';
                        if (misrataDistricts) misrataDistricts.style.display = 'none';
                        if (benghaziDistricts) benghaziDistricts.style.display = 'none';

                    } else if (selectedCity === 'مصراتة') {
                        // تفعيل قائمة المناطق لمدينة مصراتة
                        mobileDistrictSelect.disabled = false;

                        // إظهار مناطق مصراتة فقط
                        const tripoliDistricts = mobileDistrictSelect.querySelector('.tripoli-districts');
                        const misrataDistricts = mobileDistrictSelect.querySelector('.misrata-districts');
                        const benghaziDistricts = mobileDistrictSelect.querySelector('.benghazi-districts');

                        if (tripoliDistricts) tripoliDistricts.style.display = 'none';
                        if (misrataDistricts) misrataDistricts.style.display = 'block';
                        if (benghaziDistricts) benghaziDistricts.style.display = 'none';

                    } else if (selectedCity === 'بنغازي') {
                        // تفعيل قائمة المناطق لمدينة بنغازي
                        mobileDistrictSelect.disabled = false;

                        // إظهار مناطق بنغازي فقط
                        const tripoliDistricts = mobileDistrictSelect.querySelector('.tripoli-districts');
                        const misrataDistricts = mobileDistrictSelect.querySelector('.misrata-districts');
                        const benghaziDistricts = mobileDistrictSelect.querySelector('.benghazi-districts');

                        if (tripoliDistricts) tripoliDistricts.style.display = 'none';
                        if (misrataDistricts) misrataDistricts.style.display = 'none';
                        if (benghaziDistricts) benghaziDistricts.style.display = 'block';

                    } else {
                        // تعطيل قائمة المناطق إذا لم يتم اختيار مدينة
                        mobileDistrictSelect.disabled = true;
                        mobileDistrictSelect.value = '';
                    }
                }

                if (selectedCity) {
                    // إذا كان حقل العنوان فارغاً أو يحتوي فقط على اسم المدينة السابق، قم بتحديثه
                    const currentAddress = mobileLocationAddress.value.trim();
                    if (currentAddress === '' ||
                        currentAddress === 'طرابلس' ||
                        currentAddress === 'مصراتة' ||
                        currentAddress === 'بنغازي') {
                        mobileLocationAddress.value = selectedCity;
                    } else {
                        // إذا كان العنوان يحتوي على نص آخر، أضف المدينة في البداية إذا لم تكن موجودة
                        if (!currentAddress.includes('طرابلس') &&
                            !currentAddress.includes('مصراتة') &&
                            !currentAddress.includes('بنغازي')) {
                            mobileLocationAddress.value = selectedCity + ' - ' + currentAddress;
                        } else {
                            // استبدل اسم المدينة القديم بالجديد
                            let newAddress = currentAddress;
                            newAddress = newAddress.replace('طرابلس', selectedCity);
                            newAddress = newAddress.replace('مصراتة', selectedCity);
                            newAddress = newAddress.replace('بنغازي', selectedCity);
                            mobileLocationAddress.value = newAddress;
                        }
                    }
                }
            });
        }

        // إضافة مستمع حدث لقائمة المناطق في النموذج الثالث
        if (mobileDistrictSelect && mobileLocationAddress) {
            mobileDistrictSelect.addEventListener('change', function() {
                const selectedDistrict = this.value;
                const selectedCity = mobileCitySelect ? mobileCitySelect.value : '';

                if (selectedDistrict && selectedCity) {
                    // تحديث حقل العنوان ليشمل المدينة والمنطقة
                    mobileLocationAddress.value = selectedCity + ' - ' + selectedDistrict;
                }
            });
        }

        // إضافة علامة على الخريطة
        if (mobileMap) {
            // إزالة العلامة السابقة إذا وجدت
            mobileMarkers = mobileMarkers.filter(marker => {
                if (marker.options.isSelected) {
                    mobileMap.removeLayer(marker);
                    return false;
                }
                return true;
            });

            // إضافة علامة جديدة
            const marker = L.marker(latlng, {
                icon: L.divIcon({
                    html: '<i class="fas fa-map-marker-alt selected-marker"></i>',
                    iconSize: [30, 30],
                    iconAnchor: [15, 30],
                    className: 'selected-marker-container'
                }),
                isSelected: true
            }).addTo(mobileMap);

            mobileMarkers.push(marker);
        }

        // التبديل إلى تبويب النموذج
        const formTabBtn = document.querySelector('.mobile-tab-btn[data-tab="mobile-form-tab"]');
        if (formTabBtn) {
            formTabBtn.click();
        }
    }

    // تحميل المتاجر
    function loadMobileStores(listId = null) {
        console.log(`Loading mobile stores for list ${listId || 'all'}...`);

        // بناء عنوان URL
        let url = '/api/stores';
        if (listId !== null) {
            url += `?list_id=${listId}`;
        }

        // تحديث حالة زر القوائم
        const allListsBtn = document.getElementById('mobile-all-lists');
        if (allListsBtn) {
            if (listId === null) {
                allListsBtn.classList.add('active');
            } else {
                allListsBtn.classList.remove('active');
            }
        }

        // عرض مؤشر التحميل
        const storeList = document.getElementById('mobile-store-list');
        if (storeList) {
            storeList.innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2 text-muted">جاري تحميل المتاجر...</p>
                </div>
            `;
        }

        // تحميل المتاجر من الخادم
        fetch(url)
            .then(response => response.json())
            .then(data => {
                console.log('Loaded mobile stores:', data);
                mobileStores = data;
                mobileCurrentListId = listId;

                // عرض المتاجر
                renderMobileStores();
                renderMobileStoreMarkers();

                // تحديث أسماء القوائم
                updateListNames();

                // تحديث عدد المتاجر
                const storeCount = document.getElementById('mobile-store-count');
                if (storeCount) {
                    storeCount.textContent = mobileStores.length;
                }
            })
            .catch(error => {
                console.error('Error loading mobile stores:', error);
                if (storeList) {
                    storeList.innerHTML = `
                        <div class="text-center py-4">
                            <i class="fas fa-exclamation-triangle text-warning mb-3" style="font-size: 48px;"></i>
                            <p class="text-muted">حدث خطأ أثناء تحميل المتاجر. يرجى المحاولة مرة أخرى.</p>
                        </div>
                    `;
                }
            });
    }

    // عرض المتاجر في القائمة
    function renderMobileStores() {
        const storeList = document.getElementById('mobile-store-list');
        if (!storeList) return;

        if (mobileStores.length === 0) {
            storeList.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-store mb-3" style="font-size: 48px; color: var(--bs-secondary);"></i>
                    <p class="text-muted">لم تتم إضافة أي متاجر بعد. انقر على الخريطة لإضافة أول متجر!</p>
                </div>
            `;
            return;
        }

        storeList.innerHTML = '';

        mobileStores.forEach(store => {
            const storeEl = document.createElement('div');
            storeEl.className = 'card mobile-store-card';
            storeEl.dataset.storeId = store.id;

            // التأكد من وجود الإحداثيات
            const lat = store.latitude || store.lat;
            const lng = store.longitude || store.lng;

            // التأكد من وجود الإحداثيات للعرض في البطاقة
            const hasLocation = (store.latitude || store.lat) && (store.longitude || store.lng);

            storeEl.innerHTML = `
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div>
                            <h5 class="card-title">${store.name || 'متجر بدون اسم'}</h5>
                            <div class="d-flex align-items-center mb-1">
                                <i class="fas fa-phone-alt text-muted me-1" style="font-size: 0.8rem;"></i>
                                <h6 class="card-subtitle text-muted mb-0">${store.phone || 'لا يوجد رقم هاتف'}</h6>
                            </div>
                            <div class="d-flex align-items-center mb-1">
                                <i class="fas fa-layer-group text-muted me-1" style="font-size: 0.8rem;"></i>
                                <span class="badge bg-light text-dark border list-name" data-list-id="${store.list_id || 1}">جاري التحميل...</span>
                            </div>
                            ${(store.city_name || store.region_name) ? `
                            <div class="d-flex align-items-center mb-1">
                                <i class="fas fa-map-marker-alt text-muted me-1" style="font-size: 0.8rem;"></i>
                                <span class="text-muted small">
                                    ${store.city_name && store.region_name ? `${store.city_name} - ${store.region_name}` :
                                      store.city_name ? store.city_name :
                                      store.region_name ? store.region_name : ''}
                                </span>
                            </div>
                            ` : ''}
                            ${store.address ? `
                            <div class="d-flex align-items-center mb-1">
                                <i class="fas fa-info-circle text-muted me-1" style="font-size: 0.8rem;"></i>
                                <span class="text-muted small">${store.address}</span>
                            </div>
                            ` : ''}
                            ${hasLocation ? `
                                <div class="d-flex align-items-center mt-1">
                                    <i class="fas fa-map-marker-alt text-danger me-1" style="font-size: 0.8rem;"></i>
                                    <small class="text-muted">تم تحديد الموقع</small>
                                </div>
                            ` : `
                                <div class="d-flex align-items-center mt-1">
                                    <i class="fas fa-exclamation-circle text-warning me-1" style="font-size: 0.8rem;"></i>
                                    <small class="text-muted">لم يتم تحديد الموقع</small>
                                </div>
                            `}
                        </div>
                        ${store.image_path ? `
                            <img src="/${store.image_path}" alt="${store.name || 'متجر'}" class="mobile-store-image mobile-store-image-thumbnail" data-image-src="/${store.image_path}" data-store-name="${store.name || 'متجر'}" onerror="this.onerror=null; this.src='/static/img/store-placeholder.png';">
                        ` : `
                            <div class="rounded bg-light d-flex align-items-center justify-content-center mobile-store-image">
                                <i class="fas fa-store text-secondary"></i>
                            </div>
                        `}
                    </div>
                    <div class="btn-group w-100">
                        <button class="btn btn-outline-primary locate-store" data-store-id="${store.id}">
                            <i class="fas fa-map-marked-alt"></i> الموقع
                        </button>
                        <button class="btn btn-outline-secondary edit-store" data-store-id="${store.id}">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="btn btn-outline-danger delete-store" data-store-id="${store.id}">
                            <i class="fas fa-trash"></i>
                        </button>
                        <button class="btn btn-outline-success share-store" data-store-id="${store.id}">
                            <i class="fab fa-whatsapp"></i>
                        </button>
                    </div>
                </div>
            `;

            // إضافة مستمعي الأحداث

            // مستمع حدث النقر على الصورة
            const storeImage = storeEl.querySelector('.mobile-store-image-thumbnail');
            if (storeImage) {
                storeImage.addEventListener('click', () => {
                    const imageSrc = storeImage.getAttribute('data-image-src');
                    const storeName = storeImage.getAttribute('data-store-name');
                    console.log('Mobile image clicked for store:', storeName);

                    // فتح النافذة المنبثقة وعرض الصورة
                    const modalImage = document.getElementById('modalImage');
                    const modalTitle = document.getElementById('imageModalLabel');

                    if (modalImage && modalTitle) {
                        modalImage.src = imageSrc;
                        modalTitle.textContent = `صورة ${storeName}`;

                        // فتح النافذة المنبثقة
                        const imageModal = new bootstrap.Modal(document.getElementById('imageModal'));
                        imageModal.show();
                    }
                });
            }

            // زر تحديد الموقع
            const locateBtn = storeEl.querySelector('.locate-store');
            if (locateBtn) {
                locateBtn.addEventListener('click', () => {
                    locateMobileStore(store.id);
                });
            }

            const editBtn = storeEl.querySelector('.edit-store');
            if (editBtn) {
                editBtn.addEventListener('click', () => {
                    editMobileStore(store.id);
                });
            }

            const deleteBtn = storeEl.querySelector('.delete-store');
            if (deleteBtn) {
                deleteBtn.addEventListener('click', () => {
                    deleteMobileStore(store.id);
                });
            }

            const shareBtn = storeEl.querySelector('.share-store');
            if (shareBtn) {
                shareBtn.addEventListener('click', () => {
                    shareMobileStore(store.id);
                });
            }

            storeList.appendChild(storeEl);
        });
    }

    // عرض علامات المتاجر على الخريطة
    function renderMobileStoreMarkers() {
        if (!mobileMap) return;

        // إزالة العلامات السابقة (باستثناء علامة الموقع المحدد)
        mobileMarkers = mobileMarkers.filter(marker => {
            if (!marker.options.isSelected) {
                mobileMap.removeLayer(marker);
                return false;
            }
            return true;
        });

        // إضافة علامات جديدة
        mobileStores.forEach(store => {
            const lat = store.latitude || store.lat;
            const lng = store.longitude || store.lng;

            if (!lat || !lng) return;

            const marker = L.marker([lat, lng], {
                icon: L.divIcon({
                    html: '<i class="fas fa-map-marker-alt store-marker" style="color: #d50000;"></i>',
                    iconSize: [24, 24],
                    iconAnchor: [12, 24],
                    className: 'store-marker-container'
                }),
                storeId: store.id
            }).addTo(mobileMap);

            // إضافة مستمع النقر
            marker.on('click', () => {
                // عرض معلومات المتجر
                const imagePath = store.image_path ? `/${store.image_path}` : '';
                const phone = store.phone || 'لا يوجد رقم هاتف';

                marker.bindPopup(`
                    <div class="store-popup text-center">
                        <h5 class="mb-2">${store.name || 'متجر بدون اسم'}</h5>
                        ${imagePath ? `
                            <div class="mb-2 text-center">
                                <img src="${imagePath}" class="img-fluid rounded" style="max-height: 120px; max-width: 100%;" onerror="this.onerror=null; this.src='/static/img/store-placeholder.png';">
                            </div>
                        ` : ''}
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <i class="fas fa-phone-alt text-primary me-2"></i>
                            <span>${phone}</span>
                        </div>
                        <div class="d-flex align-items-center justify-content-center mb-3">
                            <i class="fas fa-layer-group text-secondary me-2"></i>
                            <span class="popup-list-name" data-list-id="${store.list_id || 1}">جاري التحميل...</span>
                        </div>
                        <div class="btn-group w-100">
                            <button class="btn btn-outline-primary mobile-popup-edit" data-store-id="${store.id}">
                                <i class="fas fa-edit"></i> تعديل
                            </button>
                            <button class="btn btn-outline-success mobile-popup-share" data-store-id="${store.id}">
                                <i class="fab fa-whatsapp"></i> مشاركة
                            </button>
                        </div>
                    </div>
                `, { maxWidth: 300 }).openPopup();

                // إضافة مستمعي الأحداث للأزرار في النافذة المنبثقة
                setTimeout(() => {
                    const editBtn = document.querySelector(`.mobile-popup-edit[data-store-id="${store.id}"]`);
                    if (editBtn) {
                        // إزالة مستمعات الأحداث السابقة لتجنب التكرار
                        const newEditBtn = editBtn.cloneNode(true);
                        editBtn.parentNode.replaceChild(newEditBtn, editBtn);

                        newEditBtn.addEventListener('click', () => {
                            editMobileStore(store.id);
                            marker.closePopup();
                        });
                    }

                    const shareBtn = document.querySelector(`.mobile-popup-share[data-store-id="${store.id}"]`);
                    if (shareBtn) {
                        // إزالة مستمعات الأحداث السابقة لتجنب التكرار
                        const newShareBtn = shareBtn.cloneNode(true);
                        shareBtn.parentNode.replaceChild(newShareBtn, shareBtn);

                        newShareBtn.addEventListener('click', () => {
                            shareMobileStore(store.id);
                            marker.closePopup();
                        });
                    }
                }, 100);
            });

            mobileMarkers.push(marker);
        });
    }

    // تحديد موقع متجر على الخريطة
    function locateMobileStore(storeId) {
        const store = mobileStores.find(s => s.id == storeId);
        if (!store || !mobileMap) return;

        const lat = store.latitude || store.lat;
        const lng = store.longitude || store.lng;

        // التبديل إلى تبويب الخريطة أولاً
        const mapTabBtn = document.querySelector('.mobile-tab-btn[data-tab="mobile-map-tab"]');
        if (mapTabBtn) {
            mapTabBtn.click();
        }

        // التحقق من وجود موقع للمتجر
        if (!lat || !lng) {
            // إذا لم يكن للمتجر موقع، عرض رسالة تنبيه
            alert('⚠️ لا يوجد موقع مضاف لهذا المتجر');
            return;
        }

        // الانتقال إلى موقع المتجر
        mobileMap.setView([lat, lng], 16, {
            animate: true,
            duration: 1
        });

        // العثور على العلامة وفتح النافذة المنبثقة
        const marker = mobileMarkers.find(m => m.options.storeId == storeId);
        if (marker) {
            marker.fire('click');
        }
    }

    // تعديل متجر
    function editMobileStore(storeId) {
        // التحقق من صلاحيات المستخدم
        const userRoleElement = document.querySelector('meta[name="user-role"]');
        const userRole = userRoleElement ? parseInt(userRoleElement.getAttribute('content')) : null;

        // إذا كان المستخدم زائراً (الدور 3)، لا يمكنه تعديل المتاجر
        if (userRole === 3) {
            return;
        }

        const store = mobileStores.find(s => s.id == storeId);
        if (!store) return;

        // ملء النموذج بالبيانات
        document.getElementById('mobile-store-id').value = store.id;
        document.getElementById('mobile-store-name').value = store.name || '';
        document.getElementById('mobile-store-phone').value = store.phone || '';

        // تحديد القائمة
        const listSelect = document.getElementById('mobile-store-list');
        if (listSelect && store.list_id) {
            listSelect.value = store.list_id;
        }

        // عرض الصورة إذا وجدت
        const imagePreview = document.getElementById('mobile-image-preview');
        if (imagePreview) {
            if (store.image_path) {
                const img = imagePreview.querySelector('img');
                if (img) {
                    img.src = `/${store.image_path}`;
                    img.onerror = function() { this.onerror = null; this.src = '/static/img/store-placeholder.png'; };
                    imagePreview.classList.remove('d-none');
                }
            } else {
                imagePreview.classList.add('d-none');
            }
        }

        // تعيين الموقع المحدد
        const lat = store.latitude || store.lat;
        const lng = store.longitude || store.lng;
        if (lat && lng) {
            mobileSelectedLocation = L.latLng(lat, lng);

            // تحديث النص
            const locationText = document.getElementById('mobile-selected-location');
            if (locationText) {
                locationText.textContent = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
                locationText.classList.remove('text-info');
                locationText.classList.add('text-success');
            }
        }

        // تحديث نص زر الإرسال
        const submitBtn = document.getElementById('mobile-submit-store');
        if (submitBtn) {
            submitBtn.innerHTML = '<i class="fas fa-save me-1"></i> تحديث المتجر';
        }

        // التبديل إلى تبويب النموذج
        const formTabBtn = document.querySelector('.mobile-tab-btn[data-tab="mobile-form-tab"]');
        if (formTabBtn) {
            formTabBtn.click();
        }
    }

    // حذف متجر
    function deleteMobileStore(storeId) {
        // التحقق من صلاحيات المستخدم
        const userRoleElement = document.querySelector('meta[name="user-role"]');
        const userRole = userRoleElement ? parseInt(userRoleElement.getAttribute('content')) : null;

        // إذا كان المستخدم زائراً (الدور 3)، لا يمكنه حذف المتاجر
        if (userRole === 3) {
            return;
        }

        if (!confirm('هل أنت متأكد من رغبتك في حذف هذا المتجر؟')) {
            return;
        }

        // إرسال طلب حذف المتجر إلى الخادم
        fetch(`/api/stores?id=${storeId}`, {
            method: 'DELETE'
        })
        .then(response => {
            if (response.ok) {
                return response.json();
            } else {
                throw new Error('Failed to delete store');
            }
        })
        .then((result) => {
            console.log('Store deleted successfully:', result);

            // إعادة تحميل المتاجر
            loadMobileStores(mobileCurrentListId);

            // عرض رسالة نجاح
            alert('تم حذف المتجر بنجاح');
        })
        .catch(error => {
            console.error('Error deleting store:', error);
            alert('حدث خطأ أثناء محاولة حذف المتجر');
        });
    }

    // مشاركة متجر
    function shareMobileStore(storeId) {
        const store = mobileStores.find(s => s.id == storeId);
        if (!store) return;

        const lat = store.latitude || store.lat;
        const lng = store.longitude || store.lng;

        if (!lat || !lng) return;

        // إنشاء رابط مشاركة
        const mapUrl = `https://www.google.com/maps?q=${lat},${lng}`;

        // إضافة عنوان المتجر
        let storeAddress = '';
        if (store.city_name && store.region_name) {
            storeAddress = `${store.city_name} - ${store.region_name}`;
        } else if (store.address) {
            storeAddress = store.address;
        }

        let message = `${store.name}\n`;
        if (storeAddress) {
            message += `العنوان: ${storeAddress}\n`;
        }
        if (store.phone) {
            message += `${store.phone}\n`;
        }
        message += `${mapUrl}`;

        const whatsappUrl = `whatsapp://send?text=${encodeURIComponent(message)}`;

        // فتح تطبيق واتساب
        window.location.href = whatsappUrl;
    }

    // التحقق من حالة حقول النموذج لإظهار أو إخفاء زر مسح النموذج
    function checkMobileFormFields() {
        const nameInput = document.getElementById('mobile-store-name');
        const phoneInput = document.getElementById('mobile-store-phone');
        const storeIdInput = document.getElementById('mobile-store-id');
        const imageInput = document.getElementById('mobile-store-image');
        const clearButton = document.getElementById('mobile-clear-form');

        if (nameInput && phoneInput && storeIdInput && clearButton) {
            // إظهار زر مسح النموذج بمجرد الضغط على أي حقل
            clearButton.classList.remove('d-none');

            // إذا كانت جميع الحقول فارغة ولم يكن هناك معرف متجر، يمكن إخفاء الزر مرة أخرى
            // لكن فقط إذا لم يكن هناك أي تفاعل مع النموذج
            if (nameInput.value.trim() === '' && phoneInput.value.trim() === '' && storeIdInput.value.trim() === '' &&
                (!imageInput || imageInput.files.length === 0) &&
                !document.activeElement.closest('#mobile-store-form')) {
                clearButton.classList.add('d-none');
            }

            // التحقق من وجود معرف متجر (في وضع التعديل)
            const editButton = document.getElementById('mobileEditStore');
            if (editButton) {
                if (storeIdInput.value.trim() !== '') {
                    // إظهار زر التعديل
                    editButton.classList.remove('d-none');
                } else {
                    // إخفاء زر التعديل
                    editButton.classList.add('d-none');
                }
            }
        }
    }

    // إعداد مستمعي الأحداث
    function setupMobileEventListeners() {
        // مستمعي أحداث التبويبات
        const tabButtons = document.querySelectorAll('.mobile-tab-btn');
        tabButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                // إزالة الفئة النشطة من جميع الأزرار
                tabButtons.forEach(b => b.classList.remove('active'));

                // إضافة الفئة النشطة للزر المنقور
                btn.classList.add('active');

                // إخفاء جميع محتويات التبويبات
                const tabContents = document.querySelectorAll('.mobile-tab-content');
                tabContents.forEach(content => content.classList.remove('active'));

                // إظهار المحتوى المطلوب
                const tabId = btn.dataset.tab;
                const tabContent = document.getElementById(tabId);
                if (tabContent) {
                    tabContent.classList.add('active');

                    // إذا كان التبويب هو الخريطة، قم بتحديث حجمها
                    if (tabId === 'mobile-map-tab' && mobileMap) {
                        setTimeout(() => {
                            mobileMap.invalidateSize();
                        }, 100);
                    }
                }
            });
        });

        // مستمع حدث زر الموقع الحالي
        const getCurrentLocationBtn = document.getElementById('mobile-get-location');
        if (getCurrentLocationBtn) {
            getCurrentLocationBtn.addEventListener('click', () => {
                if (navigator.geolocation) {
                    getCurrentLocationBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري التحديد...';
                    getCurrentLocationBtn.disabled = true;

                    navigator.geolocation.getCurrentPosition(
                        (position) => {
                            const lat = position.coords.latitude;
                            const lng = position.coords.longitude;

                            // الانتقال إلى الموقع الحالي
                            if (mobileMap) {
                                mobileMap.setView([lat, lng], 16, {
                                    animate: true,
                                    duration: 1
                                });

                                // إضافة علامة مؤقتة للموقع الحالي
                                const currentLocationMarker = L.marker([lat, lng], {
                                    icon: L.divIcon({
                                        html: '<i class="fas fa-crosshairs pulse-icon"></i>',
                                        iconSize: [24, 24],
                                        iconAnchor: [12, 12],
                                        className: 'current-location-marker'
                                    }),
                                    zIndexOffset: 1000
                                }).addTo(mobileMap);

                                // إضافة دائرة للدقة
                                const accuracyCircle = L.circle([lat, lng], {
                                    radius: position.coords.accuracy,
                                    color: '#4285F4',
                                    fillColor: '#4285F4',
                                    fillOpacity: 0.15,
                                    weight: 1
                                }).addTo(mobileMap);

                                // إزالة العلامة والدائرة بعد 5 ثواني
                                setTimeout(() => {
                                    mobileMap.removeLayer(currentLocationMarker);
                                    mobileMap.removeLayer(accuracyCircle);
                                }, 5000);
                            }

                            // إعادة الزر إلى حالته الأصلية
                            getCurrentLocationBtn.innerHTML = '<i class="fas fa-location-arrow me-1"></i> موقعي الحالي';
                            getCurrentLocationBtn.disabled = false;
                        },
                        (error) => {
                            console.error('Error getting current location:', error);
                            let errorMessage = 'حدث خطأ في تحديد الموقع';

                            switch (error.code) {
                                case error.PERMISSION_DENIED:
                                    errorMessage = 'تم رفض الوصول إلى الموقع';
                                    break;
                                case error.POSITION_UNAVAILABLE:
                                    errorMessage = 'معلومات الموقع غير متوفرة';
                                    break;
                                case error.TIMEOUT:
                                    errorMessage = 'انتهت مهلة طلب الموقع';
                                    break;
                            }

                            alert(`⚠️ ${errorMessage}`);

                            // إعادة الزر إلى حالته الأصلية
                            getCurrentLocationBtn.innerHTML = '<i class="fas fa-location-arrow me-1"></i> موقعي الحالي';
                            getCurrentLocationBtn.disabled = false;
                        },
                        {
                            enableHighAccuracy: true,
                            timeout: 10000,
                            maximumAge: 0
                        }
                    );
                } else {
                    alert('⚠️ متصفحك لا يدعم تحديد الموقع الجغرافي');
                }
            });
        }

        // مستمع حدث زر إضافة متجر جديد
        const addStoreBtn = document.getElementById('mobile-add-store');
        if (addStoreBtn) {
            addStoreBtn.addEventListener('click', () => {
                // إعادة تعيين النموذج
                resetMobileForm();

                // التبديل إلى تبويب النموذج
                const formTabBtn = document.querySelector('.mobile-tab-btn[data-tab="mobile-form-tab"]');
                if (formTabBtn) {
                    formTabBtn.click();
                }
            });
        }

        // مستمع حدث نموذج المتجر
        const storeForm = document.getElementById('mobile-store-form');
        if (storeForm) {
            storeForm.addEventListener('submit', (e) => {
                e.preventDefault();

                // التحقق من وجود معرف متجر (وضع التعديل)
                const storeId = document.getElementById('mobile-store-id').value;
                if (storeId) {
                    // إذا كان هناك معرف متجر، فهذا يعني أننا في وضع التعديل
                    // وسيتم التعامل معه بواسطة زر التعديل، لذا نتوقف هنا
                    alert('يرجى استخدام زر التعديل لتحديث المتجر الحالي');
                    return;
                }

                // إضافة متجر جديد
                submitMobileStoreForm();
            });
        }

        // مستمعي أحداث حقول النموذج لإظهار أو إخفاء زر مسح النموذج
        const nameInput = document.getElementById('mobile-store-name');
        const phoneInput = document.getElementById('mobile-store-phone');
        const listInput = document.getElementById('mobile-store-list');
        const imageInput = document.getElementById('mobile-store-image');

        if (nameInput) {
            nameInput.addEventListener('input', checkMobileFormFields);
            nameInput.addEventListener('focus', checkMobileFormFields); // إظهار الأزرار عند الضغط على الحقل
        }

        if (phoneInput) {
            phoneInput.addEventListener('input', checkMobileFormFields);
            phoneInput.addEventListener('focus', checkMobileFormFields); // إظهار الأزرار عند الضغط على الحقل
        }

        if (listInput) {
            listInput.addEventListener('focus', checkMobileFormFields); // إظهار الأزرار عند الضغط على الحقل
        }

        if (imageInput) {
            imageInput.addEventListener('focus', checkMobileFormFields); // إظهار الأزرار عند الضغط على الحقل
        }

        // مستمع حدث زر مسح النموذج
        const clearFormBtn = document.getElementById('mobile-clear-form');
        if (clearFormBtn) {
            clearFormBtn.addEventListener('click', () => {
                resetMobileForm();
            });
        }

        // إخفاء زر مسح النموذج عند تحميل الصفحة
        checkMobileFormFields();

        // مستمع حدث زر تعديل المتجر
        const editStoreBtn = document.getElementById('mobileEditStore');
        if (editStoreBtn) {
            editStoreBtn.addEventListener('click', () => {
                // التحقق من صلاحيات المستخدم
                const userRoleElement = document.querySelector('meta[name="user-role"]');
                const userRole = userRoleElement ? parseInt(userRoleElement.getAttribute('content')) : null;

                // إذا كان المستخدم زائراً (الدور 3)، لا يمكنه تعديل المتاجر
                if (userRole === 3) {
                    return;
                }

                // التحقق من وجود معرف متجر
                const storeId = document.getElementById('mobile-store-id').value;
                if (storeId) {
                    // التحقق من وجود المتجر
                    const store = mobileStores.find(s => s.id == storeId);
                    if (!store) {
                        alert('لم يتم العثور على المتجر');
                        return;
                    }

                    // تنفيذ عملية التعديل
                    submitMobileStoreForm();
                }
            });
        }

        // مستمع حدث حقل البحث
        const searchInput = document.getElementById('mobile-search-input');
        if (searchInput) {
            searchInput.addEventListener('input', () => {
                searchMobileStores(searchInput.value);
            });
        }

        // مستمع حدث زر البحث
        const searchButton = document.getElementById('mobile-search-button');
        if (searchButton) {
            searchButton.addEventListener('click', () => {
                const query = document.getElementById('mobile-search-input').value;
                searchMobileStores(query);
            });
        }

        // مستمع حدث زر مسح البحث
        const clearSearchButton = document.getElementById('mobile-clear-search');
        if (clearSearchButton) {
            clearSearchButton.addEventListener('click', () => {
                const searchInput = document.getElementById('mobile-search-input');
                searchInput.value = '';
                searchMobileStores('');
                searchInput.focus();
            });
        }

        // مستمع حدث زر جميع القوائم
        const allListsBtn = document.getElementById('mobile-all-lists');
        if (allListsBtn) {
            allListsBtn.addEventListener('click', () => {
                loadMobileStores();
            });
        }

        // مستمع حدث تغيير حقل الصورة
        if (imageInput) {
            imageInput.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        const imagePreview = document.getElementById('mobile-image-preview');
                        if (imagePreview) {
                            const img = imagePreview.querySelector('img');
                            if (img) {
                                img.src = e.target.result;
                                imagePreview.classList.remove('d-none');
                            }
                        }
                    };
                    reader.readAsDataURL(file);
                }

                // إظهار زر مسح النموذج عند تحديد صورة
                checkMobileFormFields();
            });
        }

        // مستمع حدث زر إزالة الصورة
        const removeImageBtn = document.getElementById('mobile-remove-image');
        if (removeImageBtn) {
            removeImageBtn.addEventListener('click', () => {
                const imageInput = document.getElementById('mobile-store-image');
                if (imageInput) {
                    imageInput.value = '';
                }

                const imagePreview = document.getElementById('mobile-image-preview');
                if (imagePreview) {
                    imagePreview.classList.add('d-none');
                }

                // إعادة التحقق من حالة النموذج
                checkMobileFormFields();
            });
        }

        // مستمع حدث لقائمة المدن المنسدلة
        const mobileCitySelect = document.getElementById('mobile-city-select');
        const mobileDistrictSelect = document.getElementById('mobile-district-select');
        const mobileStoreAddress = document.getElementById('mobile-store-address');

        // إصلاح مشكلة ظهور القائمة المنسدلة فوق الصندوق في واجهة الموبايل
        if (mobileDistrictSelect) {
            mobileDistrictSelect.style.position = 'relative';
            mobileDistrictSelect.style.zIndex = '1000';
        }

        if (mobileCitySelect && mobileStoreAddress) {
            mobileCitySelect.addEventListener('change', function() {
                const selectedCity = this.value;
                if (selectedCity) {
                    // إذا كان حقل العنوان فارغاً أو يحتوي فقط على اسم المدينة السابق، قم بتحديثه
                    const currentAddress = mobileStoreAddress.value.trim();
                    if (currentAddress === '' ||
                        currentAddress === 'طرابلس' ||
                        currentAddress === 'مصراتة' ||
                        currentAddress === 'بنغازي') {
                        mobileStoreAddress.value = selectedCity;
                    } else {
                        // إذا كان العنوان يحتوي على نص آخر، أضف المدينة في البداية إذا لم تكن موجودة
                        if (!currentAddress.includes('طرابلس') &&
                            !currentAddress.includes('مصراتة') &&
                            !currentAddress.includes('بنغازي')) {
                            mobileStoreAddress.value = selectedCity + ' - ' + currentAddress;
                        } else {
                            // استبدل اسم المدينة القديم بالجديد
                            let newAddress = currentAddress;
                            newAddress = newAddress.replace('طرابلس', selectedCity);
                            newAddress = newAddress.replace('مصراتة', selectedCity);
                            newAddress = newAddress.replace('بنغازي', selectedCity);
                            mobileStoreAddress.value = newAddress;
                        }
                    }
                    // تشغيل حدث input لتحديث حالة النموذج
                    mobileStoreAddress.dispatchEvent(new Event('input'));
                }
            });
        }
    }

    // إعادة تعيين نموذج المتجر
    function resetMobileForm() {
        const form = document.getElementById('mobile-store-form');
        if (form) {
            form.reset();
        }

        // إعادة تعيين الحقول الخفية
        document.getElementById('mobile-store-id').value = '';

        // إخفاء زر التعديل وزر مسح النموذج
        const editButton = document.getElementById('mobileEditStore');
        if (editButton) {
            editButton.classList.add('d-none');
        }

        // إخفاء زر مسح النموذج
        const clearButton = document.getElementById('mobile-clear-form');
        if (clearButton) {
            clearButton.classList.add('d-none');
        }

        // إخفاء معاينة الصورة
        const imagePreview = document.getElementById('mobile-image-preview');
        if (imagePreview) {
            imagePreview.classList.add('d-none');
        }

        // إعادة تعيين نص الموقع
        const locationText = document.getElementById('mobile-selected-location');
        if (locationText) {
            locationText.textContent = 'لم يتم تحديد موقع';
            locationText.classList.add('text-info');
            locationText.classList.remove('text-success');
        }

        // إعادة تعيين نص زر الإرسال
        const submitBtn = document.getElementById('mobile-submit-store');
        if (submitBtn) {
            submitBtn.innerHTML = '<i class="fas fa-save me-1"></i> حفظ المتجر';
        }

        // إعادة تعيين الموقع المحدد
        mobileSelectedLocation = null;
    }

    // إرسال نموذج المتجر
    function submitMobileStoreForm() {
        // التحقق من صلاحيات المستخدم
        const userRoleElement = document.querySelector('meta[name="user-role"]');
        const userRole = userRoleElement ? parseInt(userRoleElement.getAttribute('content')) : null;

        // الحصول على معرف المتجر للتحقق مما إذا كنا في وضع التعديل
        const storeId = document.getElementById('mobile-store-id').value;

        // إذا كان المستخدم زائراً (الدور 3) وكنا في وضع التعديل، لا يمكنه تعديل المتاجر
        if (userRole === 3 && storeId) {
            return;
        }

        // التحقق من وجود اسم المتجر
        const nameInput = document.getElementById('mobile-store-name');
        if (!nameInput || !nameInput.value.trim()) {
            alert('يرجى إدخال اسم المتجر');
            return;
        }

        // التحقق من وجود عنوان المتجر
        const fullAddressInput = document.getElementById('mobileStoreFullAddress');
        if (!fullAddressInput || !fullAddressInput.value.trim()) {
            alert('يرجى إدخال عنوان المتجر');
            return;
        }

        // التحقق من وجود موقع محدد
        if (!mobileSelectedLocation) {
            // إذا كنا في وضع التعديل، نحاول استخدام الموقع الحالي للمتجر
            if (storeId) {
                const store = mobileStores.find(s => s.id == storeId);
                if (store && (store.latitude || store.lat) && (store.longitude || store.lng)) {
                    mobileSelectedLocation = L.latLng(
                        parseFloat(store.latitude || store.lat),
                        parseFloat(store.longitude || store.lng)
                    );
                } else {
                    alert('⚠️ لم يتم تحديد موقع! يرجى النقر على الخريطة لتحديد موقع المتجر');
                    return;
                }
            } else {
                // إذا كنا في وضع الإضافة، يجب تحديد موقع
                alert('⚠️ لم يتم تحديد موقع! يرجى النقر على الخريطة لتحديد موقع المتجر');

                // تسليط الضوء على الخريطة لجذب انتباه المستخدم
                const mapContainer = document.getElementById('mobile-map');
                if (mapContainer) {
                    // التبديل إلى تبويب الخريطة
                    const mapTabBtn = document.querySelector('.mobile-tab-btn[data-tab="mobile-map-tab"]');
                    if (mapTabBtn) {
                        mapTabBtn.click();
                    }

                    // تسليط الضوء على الخريطة
                    mapContainer.classList.add('mobile-map-highlight');
                    setTimeout(() => {
                        mapContainer.classList.remove('mobile-map-highlight');
                    }, 1500);
                }
                return;
            }
        }

        // جمع بيانات النموذج
        const storeName = nameInput.value;
        const storePhone = document.getElementById('mobile-store-phone').value;
        const storeListId = document.getElementById('mobile-store-list').value;
        const storeImage = document.getElementById('mobile-store-image').files[0];
        const storeFullAddress = fullAddressInput.value;
        const storeAddress = document.getElementById('mobileStoreAddress').value;

        // الحصول على بيانات المدينة والمنطقة
        const mobileCitySelect = document.getElementById('mobile-city-select');
        const mobileDistrictSelect = document.getElementById('mobile-district-select');
        const selectedCity = mobileCitySelect ? mobileCitySelect.value : '';
        const selectedDistrict = mobileDistrictSelect ? mobileDistrictSelect.value : '';

        // الحصول على أسماء المدينة والمنطقة
        const cityName = mobileCitySelect && mobileCitySelect.selectedOptions[0] ? mobileCitySelect.selectedOptions[0].text : '';
        const regionName = mobileDistrictSelect && mobileDistrictSelect.selectedOptions[0] ? mobileDistrictSelect.selectedOptions[0].text : '';

        // إنشاء كائن FormData
        const formData = new FormData();
        formData.append('name', storeName);
        formData.append('phone', storePhone || '');
        formData.append('latitude', mobileSelectedLocation.lat);
        formData.append('longitude', mobileSelectedLocation.lng);
        formData.append('list_id', storeListId);
        formData.append('full_address', storeFullAddress);
        formData.append('address', storeAddress || '');
        formData.append('city_name', cityName);
        formData.append('region_name', regionName);
        formData.append('city_id', selectedCity);
        formData.append('region_id', selectedDistrict);

        if (storeImage) {
            formData.append('image', storeImage);
        }

        // تحديد طريقة الإرسال (إضافة أو تحديث)
        const method = storeId ? 'PUT' : 'POST';
        const url = '/api/stores';

        // إضافة معرف المتجر إذا كان تحديثًا
        if (storeId) {
            formData.append('id', storeId);
        }

        // إرسال البيانات إلى الخادم
        fetch(url, {
            method: method,
            body: formData
        })
        .then(response => {
            if (response.ok) {
                return response.json();
            } else {
                throw new Error('Failed to save store');
            }
        })
        .then(result => {
            console.log('Store saved successfully:', result);

            // إعادة تعيين النموذج
            resetMobileForm();

            // إعادة تحميل المتاجر
            loadMobileStores(mobileCurrentListId);

            // التبديل إلى تبويب الخريطة
            const mapTabBtn = document.querySelector('.mobile-tab-btn[data-tab="mobile-map-tab"]');
            if (mapTabBtn) {
                mapTabBtn.click();
            }

            // عرض رسالة نجاح
            alert(storeId ? 'تم تحديث المتجر بنجاح' : 'تم إضافة المتجر بنجاح');
        })
        .catch(error => {
            console.error('Error saving store:', error);
            alert('حدث خطأ أثناء حفظ المتجر');
        });
    }

    // البحث في المتاجر
    function searchMobileStores(query) {
        if (!query || query.trim() === '') {
            // إظهار جميع المتاجر إذا كان البحث فارغًا
            const storeCards = document.querySelectorAll('.mobile-store-card');
            storeCards.forEach(card => {
                card.style.display = 'block';
            });
            return;
        }

        // تنفيذ البحث
        const normalizedQuery = query.toLowerCase().trim();
        const storeCards = document.querySelectorAll('.mobile-store-card');

        storeCards.forEach(card => {
            const storeId = card.dataset.storeId;
            const store = mobileStores.find(s => s.id == storeId);

            if (store) {
                const storeName = (store.name || '').toLowerCase();
                const storePhone = (store.phone || '').toLowerCase();

                // إظهار أو إخفاء البطاقة بناءً على نتيجة البحث
                if (storeName.includes(normalizedQuery) || storePhone.includes(normalizedQuery)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            }
        });
    }

    // تهيئة واجهة الهاتف النقال
    function setupMobileInterface() {
        // التأكد من تحميل مكتبة Leaflet قبل تهيئة الخريطة
        if (typeof L !== 'undefined') {
            console.log('Leaflet is available, initializing mobile map');
            initMobileMap();
        } else {
            console.log('Leaflet not available yet, waiting for it to load');
            // انتظار تحميل مكتبة Leaflet
            const leafletCheckInterval = setInterval(function() {
                if (typeof L !== 'undefined') {
                    console.log('Leaflet loaded, initializing mobile map');
                    clearInterval(leafletCheckInterval);
                    initMobileMap();
                }
            }, 500);
        }

        // إعداد مستمعات الأحداث للواجهة
        setupMobileEventListeners();
    }

    // بدء تهيئة الواجهة
    setupMobileInterface();

    // التأكد من إخفاء زر التعديل عند تحميل الصفحة
    const editButton = document.getElementById('mobileEditStore');
    if (editButton) {
        editButton.classList.add('d-none');
    }

    // إضافة فئة "loaded" لواجهة الهاتف لإظهارها بسلاسة
    setTimeout(() => {
        if (mobileView) {
            mobileView.classList.add('loaded');
        }
    }, 100);
}
